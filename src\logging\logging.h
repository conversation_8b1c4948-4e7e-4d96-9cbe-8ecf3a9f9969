#ifndef LOGGING_H
#define LOGGING_H

#include <stdio.h>
#include <time.h>
#include <stdarg.h>

/**
 * 日志级别枚举
 */
typedef enum {
    LOG_LEVEL_DEBUG = 0,    // 调试信息
    LOG_LEVEL_INFO,         // 一般信息
    LOG_LEVEL_WARNING,      // 警告信息
    LOG_LEVEL_ERROR,        // 错误信息
    LOG_LEVEL_FATAL         // 致命错误
} log_level_t;

/**
 * 日志配置结构体
 */
typedef struct {
    log_level_t min_level;      // 最小日志级别
    FILE *output_file;          // 输出文件指针，NULL表示输出到stdout
    int enable_timestamp;       // 是否启用时间戳
    int enable_level_prefix;    // 是否启用级别前缀
} log_config_t;

/**
 * 初始化日志系统
 * @param config 日志配置
 * @return 0成功，-1失败
 */
int log_init(const log_config_t *config);

/**
 * 记录日志消息
 * @param level 日志级别
 * @param format 格式化字符串
 * @param ... 可变参数
 */
void log_message(log_level_t level, const char *format, ...);

/**
 * 记录系统错误（基于errno）
 * @param level 日志级别
 * @param prefix 错误前缀信息
 */
void log_perror(log_level_t level, const char *prefix);

/**
 * 设置日志级别
 * @param level 新的最小日志级别
 */
void log_set_level(log_level_t level);

/**
 * 获取当前日志级别
 * @return 当前最小日志级别
 */
log_level_t log_get_level(void);

/**
 * 关闭日志系统，释放资源
 */
void log_cleanup(void);

/**
 * 便捷宏定义
 */
#define LOG_DEBUG(fmt, ...) log_message(LOG_LEVEL_DEBUG, fmt, ##__VA_ARGS__)
#define LOG_INFO(fmt, ...) log_message(LOG_LEVEL_INFO, fmt, ##__VA_ARGS__)
#define LOG_WARNING(fmt, ...) log_message(LOG_LEVEL_WARNING, fmt, ##__VA_ARGS__)
#define LOG_ERROR(fmt, ...) log_message(LOG_LEVEL_ERROR, fmt, ##__VA_ARGS__)
#define LOG_FATAL(fmt, ...) log_message(LOG_LEVEL_FATAL, fmt, ##__VA_ARGS__)

#define LOG_PERROR_DEBUG(prefix) log_perror(LOG_LEVEL_DEBUG, prefix)
#define LOG_PERROR_INFO(prefix) log_perror(LOG_LEVEL_INFO, prefix)
#define LOG_PERROR_WARNING(prefix) log_perror(LOG_LEVEL_WARNING, prefix)
#define LOG_PERROR_ERROR(prefix) log_perror(LOG_LEVEL_ERROR, prefix)
#define LOG_PERROR_FATAL(prefix) log_perror(LOG_LEVEL_FATAL, prefix)

#endif // LOGGING_H
