build/obj/command_parser/command_parser.o: \
 src/command_parser/command_parser.c src/command_parser/command_parser.h \
 src/command_parser/../programming_controller/programming_controller.h \
 D:/work/projs/jtag_writer/src/openocd_interface/openocd_interface.h \
 src/command_parser/../communicate/remote_communicator.h \
 D:/work/projs/jtag_writer/src/programming_controller/programming_controller.h \
 src/command_parser/../logging/logging.h
src/command_parser/command_parser.h:
src/command_parser/../programming_controller/programming_controller.h:
D:/work/projs/jtag_writer/src/openocd_interface/openocd_interface.h:
src/command_parser/../communicate/remote_communicator.h:
D:/work/projs/jtag_writer/src/programming_controller/programming_controller.h:
src/command_parser/../logging/logging.h:
