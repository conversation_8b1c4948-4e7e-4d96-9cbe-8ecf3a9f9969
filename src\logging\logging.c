#include "logging.h"
#include <stdlib.h>
#include <string.h>
#include <errno.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <pthread.h>
#endif

// 全局日志配置
static log_config_t g_log_config = {
    .min_level = LOG_LEVEL_INFO,
    .output_file = NULL,
    .enable_timestamp = 1,
    .enable_level_prefix = 1
};

// 线程安全锁
#ifdef _WIN32
static CRITICAL_SECTION g_log_mutex;
static int g_mutex_initialized = 0;
#else
static pthread_mutex_t g_log_mutex = PTHREAD_MUTEX_INITIALIZER;
#endif

// 日志级别字符串
static const char* level_strings[] = {
    "DEBUG",
    "INFO",
    "WARNING",
    "ERROR",
    "FATAL"
};

/**
 * 初始化互斥锁
 */
static void init_mutex(void) {
#ifdef _WIN32
    if (!g_mutex_initialized) {
        InitializeCriticalSection(&g_log_mutex);
        g_mutex_initialized = 1;
    }
#endif
}

/**
 * 加锁
 */
static void lock_mutex(void) {
#ifdef _WIN32
    EnterCriticalSection(&g_log_mutex);
#else
    pthread_mutex_lock(&g_log_mutex);
#endif
}

/**
 * 解锁
 */
static void unlock_mutex(void) {
#ifdef _WIN32
    LeaveCriticalSection(&g_log_mutex);
#else
    pthread_mutex_unlock(&g_log_mutex);
#endif
}

/**
 * 获取当前时间字符串
 */
static void get_timestamp(char *buffer, size_t size) {
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

int log_init(const log_config_t *config) {
    if (config == NULL) {
        return -1;
    }
    
    init_mutex();
    lock_mutex();
    
    g_log_config = *config;
    
    // 如果没有指定输出文件，使用stdout
    if (g_log_config.output_file == NULL) {
        g_log_config.output_file = stdout;
    }
    
    unlock_mutex();
    return 0;
}

void log_message(log_level_t level, const char *format, ...) {
    if (level < g_log_config.min_level) {
        return;
    }
    
    lock_mutex();
    
    char timestamp[64];
    if (g_log_config.enable_timestamp) {
        get_timestamp(timestamp, sizeof(timestamp));
        fprintf(g_log_config.output_file, "[%s] ", timestamp);
    }
    
    if (g_log_config.enable_level_prefix) {
        fprintf(g_log_config.output_file, "[%s] ", level_strings[level]);
    }
    
    va_list args;
    va_start(args, format);
    vfprintf(g_log_config.output_file, format, args);
    va_end(args);
    
    fprintf(g_log_config.output_file, "\n");
    fflush(g_log_config.output_file);
    
    unlock_mutex();
}

void log_perror(log_level_t level, const char *prefix) {
    if (level < g_log_config.min_level) {
        return;
    }
    
    lock_mutex();
    
    char timestamp[64];
    if (g_log_config.enable_timestamp) {
        get_timestamp(timestamp, sizeof(timestamp));
        fprintf(g_log_config.output_file, "[%s] ", timestamp);
    }
    
    if (g_log_config.enable_level_prefix) {
        fprintf(g_log_config.output_file, "[%s] ", level_strings[level]);
    }
    
    if (prefix) {
        fprintf(g_log_config.output_file, "%s: %s\n", prefix, strerror(errno));
    } else {
        fprintf(g_log_config.output_file, "%s\n", strerror(errno));
    }
    
    fflush(g_log_config.output_file);
    
    unlock_mutex();
}

void log_set_level(log_level_t level) {
    lock_mutex();
    g_log_config.min_level = level;
    unlock_mutex();
}

log_level_t log_get_level(void) {
    return g_log_config.min_level;
}

void log_cleanup(void) {
    lock_mutex();
    
    if (g_log_config.output_file != NULL && 
        g_log_config.output_file != stdout && 
        g_log_config.output_file != stderr) {
        fclose(g_log_config.output_file);
        g_log_config.output_file = NULL;
    }
    
    unlock_mutex();
    
#ifdef _WIN32
    if (g_mutex_initialized) {
        DeleteCriticalSection(&g_log_mutex);
        g_mutex_initialized = 0;
    }
#endif
}
