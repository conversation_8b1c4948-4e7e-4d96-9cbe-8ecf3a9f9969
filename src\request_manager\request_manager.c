#include "request_manager.h"
#include "../logging/logging.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
#include <windows.h>
#include <process.h>
#else
#include <pthread.h>
#include <unistd.h>
#endif

void init_request_manager_config(request_manager_config_t *config) {
    if (config == NULL) return;
    
    config->max_concurrent_requests = 4;
    config->max_queue_size = 100;
    config->worker_thread_count = 2;
    config->request_timeout_seconds = 300;  // 5分钟
    config->cleanup_interval_seconds = 60;  // 1分钟
    config->enable_remote_requests = 1;
}

static void init_request_queue(request_queue_t *queue, int max_size) {
    if (queue == NULL) return;
    
    queue->head = NULL;
    queue->tail = NULL;
    queue->count = 0;
    queue->max_size = max_size;
}

static int enqueue_request(request_queue_t *queue, request_t *request) {
    if (queue == NULL || request == NULL) return -1;
    
    if (queue->count >= queue->max_size) {
        LOG_WARNING("Request queue is full, cannot enqueue request %d", request->request_id);
        return -1;
    }
    
    request_queue_node_t *node = malloc(sizeof(request_queue_node_t));
    if (node == NULL) {
        LOG_ERROR("Failed to allocate memory for queue node");
        return -1;
    }
    
    node->request = request;
    node->next = NULL;
    
    if (queue->tail == NULL) {
        queue->head = queue->tail = node;
    } else {
        queue->tail->next = node;
        queue->tail = node;
    }
    
    queue->count++;
    LOG_DEBUG("Enqueued request %d, queue size: %d", request->request_id, queue->count);
    return 0;
}

static request_t* dequeue_request(request_queue_t *queue) {
    if (queue == NULL || queue->head == NULL) return NULL;
    
    request_queue_node_t *node = queue->head;
    request_t *request = node->request;
    
    queue->head = node->next;
    if (queue->head == NULL) {
        queue->tail = NULL;
    }
    
    queue->count--;
    free(node);
    
    LOG_DEBUG("Dequeued request %d, queue size: %d", request->request_id, queue->count);
    return request;
}

request_t* create_request(const parsed_command_t *command,
                         progress_callback_t callback,
                         void *user_data) {
    if (command == NULL) return NULL;
    
    request_t *request = malloc(sizeof(request_t));
    if (request == NULL) {
        LOG_ERROR("Failed to allocate memory for request");
        return NULL;
    }
    
    memset(request, 0, sizeof(request_t));
    
    // 复制命令
    request->command = *command;
    
    // 设置基本属性
    request->status = REQUEST_STATUS_PENDING;
    request->priority = REQUEST_PRIORITY_NORMAL;
    request->created_time = time(NULL);
    request->progress_callback = callback;
    request->user_data = user_data;
    
    // 确定请求类型
    if (command->exec_mode == EXEC_MODE_REMOTE || 
        (command->exec_mode == EXEC_MODE_AUTO && command->remote_config.host != NULL)) {
        request->type = REQUEST_TYPE_REMOTE;
    } else {
        request->type = REQUEST_TYPE_LOCAL;
    }
    
    LOG_DEBUG("Created request with type %s", 
              request->type == REQUEST_TYPE_LOCAL ? "LOCAL" : "REMOTE");
    
    return request;
}

void free_request(request_t *request) {
    if (request == NULL) return;
    
    free_parsed_command(&request->command);
    
    if (request->prog_context) {
        programming_free_context(request->prog_context);
    }
    
    if (request->remote_conn) {
        remote_disconnect(request->remote_conn);
    }
    
    free(request->error_message);
    free(request);
}

static int execute_local_request(request_t *request) {
    if (request == NULL) return -1;
    
    LOG_INFO("Executing local request %d", request->request_id);
    
    switch (request->command.type) {
        case CMD_TYPE_PROGRAM:
        case CMD_TYPE_ERASE:
        case CMD_TYPE_VERIFY:
        case CMD_TYPE_READ:
        case CMD_TYPE_RESET:
            // 执行编程操作
            return programming_control(&request->command.prog_request, 
                                     request->progress_callback);
            
        case CMD_TYPE_STATUS:
            LOG_INFO("Status command executed");
            return 0;
            
        case CMD_TYPE_VERSION:
            show_version();
            return 0;
            
        case CMD_TYPE_HELP:
            show_command_help(CMD_TYPE_UNKNOWN);
            return 0;
            
        default:
            LOG_ERROR("Unsupported command type for local execution: %d", 
                      request->command.type);
            return -1;
    }
}

static int execute_remote_request(request_t *request) {
    if (request == NULL) return -1;
    
    LOG_INFO("Executing remote request %d", request->request_id);
    
    // 建立远程连接
    int ret = remote_connect(&request->command.remote_config, &request->remote_conn);
    if (ret != 0) {
        LOG_ERROR("Failed to connect to remote host");
        return -1;
    }
    
    switch (request->command.type) {
        case CMD_TYPE_PROGRAM:
        case CMD_TYPE_ERASE:
        case CMD_TYPE_VERIFY:
        case CMD_TYPE_READ:
        case CMD_TYPE_RESET: {
            // 构建远程编程请求
            remote_programming_request_t remote_req;
            memset(&remote_req, 0, sizeof(remote_req));
            remote_req.prog_request = request->command.prog_request;
            remote_req.session_id = malloc(64);
            snprintf(remote_req.session_id, 64, "session_%d_%lld",
                     request->request_id, (long long)time(NULL));
            remote_req.async_mode = 0; // 同步模式
            
            ret = remote_program_device(request->remote_conn, &remote_req,
                                       request->progress_callback, request->user_data);
            
            free(remote_req.session_id);
            break;
        }
        
        case CMD_TYPE_VERSION: {
            char version_buffer[256];
            ret = remote_get_version(request->remote_conn, version_buffer, sizeof(version_buffer));
            if (ret == 0) {
                printf("Remote version: %s\n", version_buffer);
            }
            break;
        }
        
        case CMD_TYPE_STATUS:
            ret = remote_ping(request->remote_conn);
            if (ret == 0) {
                printf("Remote host is responding\n");
            }
            break;
            
        default:
            LOG_ERROR("Unsupported command type for remote execution: %d", 
                      request->command.type);
            ret = -1;
            break;
    }
    
    return ret;
}

static int execute_request(request_t *request) {
    if (request == NULL) return -1;
    
    request->status = REQUEST_STATUS_RUNNING;
    request->start_time = time(NULL);
    
    int result;
    if (request->type == REQUEST_TYPE_LOCAL) {
        result = execute_local_request(request);
    } else {
        result = execute_remote_request(request);
    }
    
    request->end_time = time(NULL);
    
    if (result == 0) {
        request->status = REQUEST_STATUS_COMPLETED;
        LOG_INFO("Request %d completed successfully in %ld seconds", 
                 request->request_id, request->end_time - request->start_time);
    } else {
        request->status = REQUEST_STATUS_FAILED;
        LOG_ERROR("Request %d failed after %ld seconds", 
                  request->request_id, request->end_time - request->start_time);
    }
    
    return result;
}

#ifdef _WIN32
static unsigned __stdcall worker_thread_func(void *arg) {
#else
static void* worker_thread_func(void *arg) {
#endif
    worker_thread_t *worker = (worker_thread_t*)arg;
    request_manager_t *manager = (request_manager_t*)worker->user_data;

    LOG_INFO("Worker thread %d started", worker->thread_id);

    while (!manager->shutdown_requested) {
        request_t *request = NULL;

        // 获取锁并检查队列
#ifdef _WIN32
        EnterCriticalSection(&manager->mutex);

        if (manager->pending_queue.count > 0) {
            request = dequeue_request(&manager->pending_queue);
        }

        LeaveCriticalSection(&manager->mutex);

        if (request == NULL) {
            // 等待新请求
            WaitForSingleObject(manager->queue_event, 1000);
            continue;
        }
#else
        pthread_mutex_lock(&manager->mutex);

        while (manager->pending_queue.count == 0 && !manager->shutdown_requested) {
            pthread_cond_wait(&manager->queue_cond, &manager->mutex);
        }

        if (manager->shutdown_requested) {
            pthread_mutex_unlock(&manager->mutex);
            break;
        }

        request = dequeue_request(&manager->pending_queue);
        pthread_mutex_unlock(&manager->mutex);

        if (request == NULL) {
            continue;
        }
#endif

        // 执行请求
        worker->status = WORKER_STATUS_BUSY;
        worker->current_request = request;
        worker->last_activity = time(NULL);

        execute_request(request);

        worker->current_request = NULL;
        worker->status = WORKER_STATUS_IDLE;
    }

    worker->status = WORKER_STATUS_STOPPED;
    LOG_INFO("Worker thread %d stopped", worker->thread_id);

#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

int request_manager_init(const request_manager_config_t *config, request_manager_t **manager) {
    if (config == NULL || manager == NULL) {
        LOG_ERROR("Invalid parameters for request_manager_init");
        return -1;
    }

    request_manager_t *mgr = malloc(sizeof(request_manager_t));
    if (mgr == NULL) {
        LOG_ERROR("Failed to allocate memory for request manager");
        return -1;
    }

    memset(mgr, 0, sizeof(request_manager_t));
    mgr->config = *config;
    mgr->next_request_id = 1;

    // 初始化队列
    init_request_queue(&mgr->pending_queue, config->max_queue_size);

    // 分配活动请求数组
    mgr->active_requests = malloc(sizeof(request_t*) * config->max_concurrent_requests);
    if (mgr->active_requests == NULL) {
        LOG_ERROR("Failed to allocate memory for active requests array");
        free(mgr);
        return -1;
    }
    memset(mgr->active_requests, 0, sizeof(request_t*) * config->max_concurrent_requests);

    // 初始化同步对象
#ifdef _WIN32
    InitializeCriticalSection(&mgr->mutex);
    mgr->queue_event = CreateEvent(NULL, FALSE, FALSE, NULL);
    if (mgr->queue_event == NULL) {
        LOG_ERROR("Failed to create queue event");
        DeleteCriticalSection(&mgr->mutex);
        free(mgr->active_requests);
        free(mgr);
        return -1;
    }
#else
    if (pthread_mutex_init(&mgr->mutex, NULL) != 0) {
        LOG_ERROR("Failed to initialize mutex");
        free(mgr->active_requests);
        free(mgr);
        return -1;
    }

    if (pthread_cond_init(&mgr->queue_cond, NULL) != 0) {
        LOG_ERROR("Failed to initialize condition variable");
        pthread_mutex_destroy(&mgr->mutex);
        free(mgr->active_requests);
        free(mgr);
        return -1;
    }
#endif

    // 创建工作线程
    mgr->workers = malloc(sizeof(worker_thread_t) * config->worker_thread_count);
    if (mgr->workers == NULL) {
        LOG_ERROR("Failed to allocate memory for worker threads");
        request_manager_shutdown(mgr);
        return -1;
    }

    for (int i = 0; i < config->worker_thread_count; i++) {
        worker_thread_t *worker = &mgr->workers[i];
        memset(worker, 0, sizeof(worker_thread_t));
        worker->thread_id = i;
        worker->status = WORKER_STATUS_IDLE;
        worker->user_data = mgr;

#ifdef _WIN32
        worker->thread_handle = (HANDLE)_beginthreadex(NULL, 0, worker_thread_func,
                                                      worker, 0, NULL);
        if (worker->thread_handle == 0) {
            LOG_ERROR("Failed to create worker thread %d", i);
            request_manager_shutdown(mgr);
            return -1;
        }
#else
        if (pthread_create(&worker->thread_handle, NULL, worker_thread_func, worker) != 0) {
            LOG_ERROR("Failed to create worker thread %d", i);
            request_manager_shutdown(mgr);
            return -1;
        }
#endif
    }

    *manager = mgr;
    LOG_INFO("Request manager initialized with %d worker threads", config->worker_thread_count);
    return 0;
}

void request_manager_shutdown(request_manager_t *manager) {
    if (manager == NULL) return;

    LOG_INFO("Shutting down request manager...");

    // 设置关闭标志
    manager->shutdown_requested = 1;

    // 唤醒所有工作线程
#ifdef _WIN32
    SetEvent(manager->queue_event);
#else
    pthread_cond_broadcast(&manager->queue_cond);
#endif

    // 等待工作线程结束
    for (int i = 0; i < manager->config.worker_thread_count; i++) {
        worker_thread_t *worker = &manager->workers[i];
        if (worker->thread_handle) {
#ifdef _WIN32
            WaitForSingleObject(worker->thread_handle, INFINITE);
            CloseHandle(worker->thread_handle);
#else
            pthread_join(worker->thread_handle, NULL);
#endif
        }
    }

    // 清理资源
    free(manager->workers);
    free(manager->active_requests);

    // 清理队列中的请求
    while (manager->pending_queue.head) {
        request_t *request = dequeue_request(&manager->pending_queue);
        if (request) {
            free_request(request);
        }
    }

    // 清理同步对象
#ifdef _WIN32
    DeleteCriticalSection(&manager->mutex);
    CloseHandle(manager->queue_event);
#else
    pthread_mutex_destroy(&manager->mutex);
    pthread_cond_destroy(&manager->queue_cond);
#endif

    free(manager);
    LOG_INFO("Request manager shutdown complete");
}

uint32_t request_manager_submit(request_manager_t *manager,
                               const parsed_command_t *command,
                               progress_callback_t callback,
                               void *user_data) {
    if (manager == NULL || command == NULL) {
        LOG_ERROR("Invalid parameters for request_manager_submit");
        return 0;
    }

    // 创建请求
    request_t *request = create_request(command, callback, user_data);
    if (request == NULL) {
        LOG_ERROR("Failed to create request");
        return 0;
    }

    // 分配请求ID
#ifdef _WIN32
    EnterCriticalSection(&manager->mutex);
#else
    pthread_mutex_lock(&manager->mutex);
#endif

    request->request_id = manager->next_request_id++;

    // 添加到队列
    if (enqueue_request(&manager->pending_queue, request) != 0) {
        LOG_ERROR("Failed to enqueue request");
#ifdef _WIN32
        LeaveCriticalSection(&manager->mutex);
#else
        pthread_mutex_unlock(&manager->mutex);
#endif
        free_request(request);
        return 0;
    }

    uint32_t request_id = request->request_id;

#ifdef _WIN32
    LeaveCriticalSection(&manager->mutex);
    SetEvent(manager->queue_event);
#else
    pthread_mutex_unlock(&manager->mutex);
    pthread_cond_signal(&manager->queue_cond);
#endif

    LOG_INFO("Submitted request %d to queue", request_id);
    return request_id;
}

int request_manager_get_status(request_manager_t *manager,
                              uint32_t request_id,
                              request_status_t *status) {
    if (manager == NULL || status == NULL) {
        return -1;
    }

    // 这里简化实现，实际应该维护一个请求历史记录
    *status = REQUEST_STATUS_COMPLETED;
    return 0;
}

int request_manager_wait(request_manager_t *manager,
                        uint32_t request_id,
                        int timeout_ms) {
    if (manager == NULL) {
        return -1;
    }

    // 简化实现：等待一段时间
#ifdef _WIN32
    Sleep(timeout_ms > 0 ? timeout_ms : 1000);
#else
    usleep((timeout_ms > 0 ? timeout_ms : 1000) * 1000);
#endif

    return 0;
}
