#include "programming_controller.h"
#include "../logging/logging.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <pthread.h>
#endif

// 步骤描述字符串
static const char* step_descriptions[] = {
    "Initializing programming session",
    "Connecting to target device",
    "Erasing target memory",
    "Writing firmware data",
    "Verifying written data",
    "Resetting target device",
    "Programming completed successfully",
    "Programming failed with error"
};

void init_programming_request(programming_request_t *request) {
    if (request == NULL) return;
    
    memset(request, 0, sizeof(programming_request_t));
    init_programming_options(&request->options);
}

void free_programming_request(programming_request_t *request) {
    if (request == NULL) return;
    
    free(request->jtag_config.interface_name);
    free(request->jtag_config.interface_config);
    free(request->jtag_config.transport);
    free(request->target_config.target_config);
    free(request->target_config.chip_name);
    free(request->firmware_file);
    
    memset(request, 0, sizeof(programming_request_t));
}

void init_programming_options(programming_options_t *options) {
    if (options == NULL) return;
    
    options->enable_erase = 1;
    options->enable_verify = 1;
    options->enable_reset = 1;
    options->auto_retry = 1;
    options->max_retry_count = 3;
    options->retry_delay_ms = 1000;
    options->progress_callback_interval = 100;
}

static void init_programming_progress(programming_progress_t *progress) {
    if (progress == NULL) return;
    
    memset(progress, 0, sizeof(programming_progress_t));
    progress->current_step = PROG_STEP_INIT;
    progress->status = PROG_STATUS_IDLE;
    progress->step_description = strdup(step_descriptions[PROG_STEP_INIT]);
}

static void update_progress(programming_context_t *context, 
                           programming_step_t step, 
                           int progress_percent,
                           const char *error_msg) {
    if (context == NULL) return;
    
    context->progress.current_step = step;
    context->progress.progress_percent = progress_percent;
    
    // 更新步骤描述
    free(context->progress.step_description);
    context->progress.step_description = strdup(step_descriptions[step]);
    
    // 更新错误消息
    free(context->progress.error_message);
    context->progress.error_message = error_msg ? strdup(error_msg) : NULL;
    
    // 更新状态
    if (step == PROG_STEP_ERROR) {
        context->progress.status = PROG_STATUS_FAILED;
    } else if (step == PROG_STEP_COMPLETE) {
        context->progress.status = PROG_STATUS_SUCCESS;
        context->progress.progress_percent = 100;
    } else {
        context->progress.status = PROG_STATUS_RUNNING;
    }
    
    // 调用回调函数
    if (context->callback) {
        int result = context->callback(&context->progress, context->request.user_data);
        if (result != 0) {
            context->cancelled = 1;
            LOG_INFO("Programming cancelled by user callback");
        }
    }
}

static int execute_programming_step(programming_context_t *context, programming_step_t step) {
    if (context == NULL || context->cancelled) {
        return -1;
    }
    
    openocd_cmd_params_t params;
    init_openocd_params(&params);
    
    // 复制配置
    params.jtag_config = context->request.jtag_config;
    params.target_config = context->request.target_config;
    
    int progress_base = 0;
    switch (step) {
        case PROG_STEP_CONNECT:
            progress_base = 10;
            params.operation = OPENOCD_OP_HALT;
            update_progress(context, step, progress_base, NULL);
            break;
            
        case PROG_STEP_ERASE:
            if (!context->request.options.enable_erase) {
                return 0; // 跳过擦除步骤
            }
            progress_base = 20;
            params.operation = OPENOCD_OP_ERASE;
            update_progress(context, step, progress_base, NULL);
            break;
            
        case PROG_STEP_WRITE:
            progress_base = 40;
            params.operation = OPENOCD_OP_WRITE;
            params.file_path = strdup(context->request.firmware_file);
            params.address = context->request.target_address;
            update_progress(context, step, progress_base, NULL);
            break;
            
        case PROG_STEP_VERIFY:
            if (!context->request.options.enable_verify) {
                return 0; // 跳过校验步骤
            }
            progress_base = 80;
            params.operation = OPENOCD_OP_VERIFY;
            params.file_path = strdup(context->request.firmware_file);
            params.address = context->request.target_address;
            update_progress(context, step, progress_base, NULL);
            break;
            
        case PROG_STEP_RESET:
            if (!context->request.options.enable_reset) {
                return 0; // 跳过复位步骤
            }
            progress_base = 90;
            params.operation = OPENOCD_OP_RESET;
            update_progress(context, step, progress_base, NULL);
            break;
            
        default:
            LOG_ERROR("Unknown programming step: %d", step);
            free_openocd_params(&params);
            return -1;
    }
    
    // 构建并执行OpenOCD命令
    char cmd_buffer[2048];
    int ret = build_openocd_cmd(&params, cmd_buffer, sizeof(cmd_buffer));
    
    if (ret == 0) {
        openocd_result_t result;
        ret = run_openocd_cmd(cmd_buffer, &result);
        
        if (ret == 0 && result.exit_code == 0) {
            LOG_INFO("Programming step %s completed successfully", step_descriptions[step]);
            update_progress(context, step, progress_base + 10, NULL);
        } else {
            char error_msg[512];
            snprintf(error_msg, sizeof(error_msg), 
                    "Step %s failed: exit_code=%d", 
                    step_descriptions[step], result.exit_code);
            LOG_ERROR("%s", error_msg);
            update_progress(context, PROG_STEP_ERROR, context->progress.progress_percent, error_msg);
            ret = -1;
        }
        
        free_openocd_result(&result);
    } else {
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), 
                "Failed to build command for step %s", step_descriptions[step]);
        LOG_ERROR("%s", error_msg);
        update_progress(context, PROG_STEP_ERROR, context->progress.progress_percent, error_msg);
    }
    
    free_openocd_params(&params);
    return ret;
}

int validate_programming_request(const programming_request_t *request) {
    if (request == NULL) {
        LOG_ERROR("Programming request is NULL");
        return -1;
    }

    if (request->firmware_file == NULL || strlen(request->firmware_file) == 0) {
        LOG_ERROR("Firmware file path is required");
        return -1;
    }

    // 检查文件是否存在
    FILE *file = fopen(request->firmware_file, "rb");
    if (file == NULL) {
        LOG_ERROR("Cannot open firmware file: %s", request->firmware_file);
        return -1;
    }
    fclose(file);

    if (request->jtag_config.interface_name == NULL) {
        LOG_ERROR("JTAG interface name is required");
        return -1;
    }

    return 0;
}

int programming_control(const programming_request_t *request, progress_callback_t callback) {
    if (validate_programming_request(request) != 0) {
        return -1;
    }

    // 创建编程上下文
    programming_context_t context;
    memset(&context, 0, sizeof(context));
    context.request = *request;
    context.callback = callback;

    init_programming_progress(&context.progress);

    clock_t start_time = clock();

    LOG_INFO("Starting programming operation for file: %s", request->firmware_file);

    // 执行编程步骤序列
    programming_step_t steps[] = {
        PROG_STEP_CONNECT,
        PROG_STEP_ERASE,
        PROG_STEP_WRITE,
        PROG_STEP_VERIFY,
        PROG_STEP_RESET
    };

    int num_steps = sizeof(steps) / sizeof(steps[0]);
    int retry_count = 0;
    int success = 0;

    while (!success && retry_count <= request->options.max_retry_count && !context.cancelled) {
        if (retry_count > 0) {
            LOG_INFO("Retrying programming operation (attempt %d/%d)",
                     retry_count + 1, request->options.max_retry_count + 1);

            // 重试延迟
            if (request->options.retry_delay_ms > 0) {
#ifdef _WIN32
                Sleep(request->options.retry_delay_ms);
#else
                usleep(request->options.retry_delay_ms * 1000);
#endif
            }
        }

        success = 1;
        for (int i = 0; i < num_steps && !context.cancelled; i++) {
            if (execute_programming_step(&context, steps[i]) != 0) {
                success = 0;
                break;
            }
        }

        if (!success && request->options.auto_retry) {
            retry_count++;
        } else {
            break;
        }
    }

    clock_t end_time = clock();
    context.progress.elapsed_time_ms = (int)((end_time - start_time) * 1000 / CLOCKS_PER_SEC);

    if (context.cancelled) {
        LOG_INFO("Programming operation was cancelled");
        update_progress(&context, PROG_STEP_ERROR, context.progress.progress_percent, "Operation cancelled");
        return -1;
    } else if (success) {
        LOG_INFO("Programming operation completed successfully in %d ms",
                 context.progress.elapsed_time_ms);
        update_progress(&context, PROG_STEP_COMPLETE, 100, NULL);
        return 0;
    } else {
        LOG_ERROR("Programming operation failed after %d retries", retry_count);
        return -1;
    }
}

int estimate_programming_time(const programming_request_t *request) {
    if (request == NULL || request->firmware_file == NULL) {
        return -1;
    }

    // 获取文件大小
    FILE *file = fopen(request->firmware_file, "rb");
    if (file == NULL) {
        return -1;
    }

    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fclose(file);

    if (file_size <= 0) {
        return -1;
    }

    // 基于文件大小的简单估算（每KB约100ms）
    int estimated_ms = (int)(file_size / 1024) * 100;

    // 添加固定开销
    estimated_ms += 5000; // 连接和初始化开销

    if (request->options.enable_erase) {
        estimated_ms += 2000; // 擦除开销
    }

    if (request->options.enable_verify) {
        estimated_ms += (int)(file_size / 1024) * 50; // 校验时间约为写入时间的一半
    }

    if (request->options.enable_reset) {
        estimated_ms += 1000; // 复位开销
    }

    return estimated_ms;
}
