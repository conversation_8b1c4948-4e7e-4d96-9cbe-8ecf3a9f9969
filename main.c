#include "src/logging/logging.h"
#include "src/command_parser/command_parser.h"
#include "src/request_manager/request_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>

#ifdef _WIN32
#include <windows.h>
#include <conio.h>
#else
#include <unistd.h>
#include <termios.h>
#endif

// 全局变量
static request_manager_t *g_request_manager = NULL;
static int g_interactive_mode = 0;
static int g_shutdown_requested = 0;

// 信号处理函数
void signal_handler(int signal) {
    LOG_INFO("Received signal %d, shutting down...", signal);
    g_shutdown_requested = 1;
    
    if (g_request_manager) {
        request_manager_shutdown(g_request_manager);
    }
}

// 进度回调函数
int progress_callback(const programming_progress_t *progress, void *user_data) {
    if (progress == NULL) return 0;
    
    printf("\r[%s] %d%% - %s", 
           progress->status == PROG_STATUS_SUCCESS ? "DONE" :
           progress->status == PROG_STATUS_FAILED ? "FAIL" :
           progress->status == PROG_STATUS_CANCELLED ? "CANCEL" : "PROG",
           progress->progress_percent,
           progress->step_description ? progress->step_description : "");
    
    if (progress->status == PROG_STATUS_SUCCESS ||
        progress->status == PROG_STATUS_FAILED ||
        progress->status == PROG_STATUS_CANCELLED) {
        printf("\n");
    }
    
    fflush(stdout);
    return 0;
}

// 执行单个命令
int execute_command(const parsed_command_t *command) {
    if (command == NULL) return -1;
    
    // 处理不需要请求管理器的命令
    switch (command->type) {
        case CMD_TYPE_HELP:
            show_command_help(CMD_TYPE_UNKNOWN);
            return 0;
            
        case CMD_TYPE_VERSION:
            show_version();
            return 0;
            
        case CMD_TYPE_EXIT:
            g_shutdown_requested = 1;
            return 0;
            
        default:
            break;
    }
    
    // 提交请求到请求管理器
    if (g_request_manager == NULL) {
        LOG_ERROR("Request manager not initialized");
        return -1;
    }
    
    uint32_t request_id = request_manager_submit(g_request_manager, command, 
                                               progress_callback, NULL);
    if (request_id == 0) {
        LOG_ERROR("Failed to submit request");
        return -1;
    }
    
    LOG_INFO("Submitted request %d", request_id);
    
    // 等待请求完成
    int result = request_manager_wait(g_request_manager, request_id, 0);
    if (result != 0) {
        LOG_ERROR("Request %d failed or timed out", request_id);
        return -1;
    }
    
    // 获取最终状态
    request_status_t status;
    if (request_manager_get_status(g_request_manager, request_id, &status) == 0) {
        if (status == REQUEST_STATUS_COMPLETED) {
            LOG_INFO("Request %d completed successfully", request_id);
            return 0;
        } else {
            LOG_ERROR("Request %d failed with status %d", request_id, status);
            return -1;
        }
    }
    
    return -1;
}

// 交互模式主循环
void interactive_mode(void) {
    char input_line[1024];
    parsed_command_t command;
    
    printf("JTAG Writer Interactive Mode\n");
    printf("Type 'help' for available commands, 'exit' to quit.\n\n");
    
    while (!g_shutdown_requested) {
        printf("jtag> ");
        fflush(stdout);
        
        if (fgets(input_line, sizeof(input_line), stdin) == NULL) {
            break;
        }
        
        // 移除换行符
        size_t len = strlen(input_line);
        if (len > 0 && input_line[len-1] == '\n') {
            input_line[len-1] = '\0';
        }
        
        // 跳过空行
        if (strlen(input_line) == 0) {
            continue;
        }
        
        // 解析命令
        init_parsed_command(&command);
        if (parse_interactive_command(input_line, &command) != 0) {
            printf("Error: Invalid command. Type 'help' for usage.\n");
            free_parsed_command(&command);
            continue;
        }
        
        // 执行命令
        execute_command(&command);
        free_parsed_command(&command);
    }
}

// 命令行模式
int command_line_mode(int argc, char *argv[]) {
    command_args_t args;
    parsed_command_t command;
    
    args.argc = argc;
    args.argv = argv;
    args.input_string = NULL;
    
    init_parsed_command(&command);
    
    // 解析命令行参数
    if (parse_command(&args, &command) != 0) {
        LOG_ERROR("Failed to parse command line arguments");
        show_usage();
        free_parsed_command(&command);
        return -1;
    }
    
    // 验证命令
    if (validate_parsed_command(&command) != 0) {
        LOG_ERROR("Invalid command parameters");
        free_parsed_command(&command);
        return -1;
    }
    
    // 执行命令
    int result = execute_command(&command);
    free_parsed_command(&command);
    
    return result;
}

int main(int argc, char *argv[]) {
    int result = 0;
    
    // 初始化日志系统
    if (logging_init() != 0) {
        fprintf(stderr, "Failed to initialize logging system\n");
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    LOG_INFO("JTAG Writer Frontend starting...");
    
    // 检查是否为交互模式
    if (argc == 1 || (argc == 2 && strcmp(argv[1], "-i") == 0)) {
        g_interactive_mode = 1;
    }
    
    // 初始化请求管理器
    request_manager_config_t config;
    init_request_manager_config(&config);
    
    if (request_manager_init(&config, &g_request_manager) != 0) {
        LOG_ERROR("Failed to initialize request manager");
        result = 1;
        goto cleanup;
    }
    
    // 根据模式执行
    if (g_interactive_mode) {
        interactive_mode();
    } else {
        result = command_line_mode(argc, argv);
    }
    
cleanup:
    // 清理资源
    if (g_request_manager) {
        request_manager_shutdown(g_request_manager);
        g_request_manager = NULL;
    }
    
    logging_cleanup();
    
    LOG_INFO("JTAG Writer Frontend exiting with code %d", result);
    return result;
}
