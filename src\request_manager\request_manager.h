#ifndef REQUEST_MANAGER_H
#define REQUEST_MANAGER_H

#include "../command_parser/command_parser.h"
#include "../programming_controller/programming_controller.h"
#include "../communicate/remote_communicator.h"
#include <stdint.h>

/**
 * 请求状态枚举
 */
typedef enum {
    REQUEST_STATUS_PENDING = 0,     // 等待中
    REQUEST_STATUS_RUNNING,         // 执行中
    REQUEST_STATUS_COMPLETED,       // 已完成
    REQUEST_STATUS_FAILED,          // 失败
    REQUEST_STATUS_CANCELLED        // 已取消
} request_status_t;

/**
 * 请求优先级枚举
 */
typedef enum {
    REQUEST_PRIORITY_LOW = 0,       // 低优先级
    REQUEST_PRIORITY_NORMAL,        // 普通优先级
    REQUEST_PRIORITY_HIGH,          // 高优先级
    REQUEST_PRIORITY_URGENT         // 紧急优先级
} request_priority_t;

/**
 * 请求类型枚举
 */
typedef enum {
    REQUEST_TYPE_LOCAL = 0,         // 本地请求
    REQUEST_TYPE_REMOTE             // 远程请求
} request_type_t;

/**
 * 请求结构体
 */
typedef struct {
    uint32_t request_id;            // 请求ID
    request_type_t type;            // 请求类型
    request_status_t status;        // 请求状态
    request_priority_t priority;    // 优先级
    parsed_command_t command;       // 解析后的命令
    programming_context_t *prog_context; // 编程上下文
    remote_connection_t *remote_conn;     // 远程连接
    time_t created_time;            // 创建时间
    time_t start_time;              // 开始时间
    time_t end_time;                // 结束时间
    char *error_message;            // 错误消息
    void *user_data;                // 用户数据
    progress_callback_t progress_callback; // 进度回调
} request_t;

/**
 * 请求队列节点
 */
typedef struct request_queue_node {
    request_t *request;
    struct request_queue_node *next;
} request_queue_node_t;

/**
 * 请求队列
 */
typedef struct {
    request_queue_node_t *head;     // 队列头
    request_queue_node_t *tail;     // 队列尾
    int count;                      // 队列长度
    int max_size;                   // 最大队列长度
} request_queue_t;

/**
 * 工作线程状态枚举
 */
typedef enum {
    WORKER_STATUS_IDLE = 0,         // 空闲
    WORKER_STATUS_BUSY,             // 忙碌
    WORKER_STATUS_STOPPING,         // 正在停止
    WORKER_STATUS_STOPPED           // 已停止
} worker_status_t;

/**
 * 工作线程结构体
 */
typedef struct {
    int thread_id;                  // 线程ID
    worker_status_t status;         // 状态
    request_t *current_request;     // 当前处理的请求
    time_t last_activity;           // 最后活动时间
#ifdef _WIN32
    HANDLE thread_handle;           // Windows线程句柄
#else
    pthread_t thread_handle;        // POSIX线程句柄
#endif
} worker_thread_t;

/**
 * 请求管理器配置
 */
typedef struct {
    int max_concurrent_requests;    // 最大并发请求数
    int max_queue_size;             // 最大队列大小
    int worker_thread_count;        // 工作线程数量
    int request_timeout_seconds;    // 请求超时时间
    int cleanup_interval_seconds;   // 清理间隔
    int enable_remote_requests;     // 是否启用远程请求
} request_manager_config_t;

/**
 * 请求管理器结构体
 */
typedef struct {
    request_manager_config_t config;    // 配置
    request_queue_t pending_queue;      // 待处理队列
    request_t **active_requests;        // 活动请求数组
    int active_request_count;           // 活动请求数量
    worker_thread_t *workers;           // 工作线程数组
    uint32_t next_request_id;           // 下一个请求ID
    int shutdown_requested;             // 是否请求关闭
    
#ifdef _WIN32
    CRITICAL_SECTION mutex;             // Windows互斥锁
    HANDLE queue_event;                 // 队列事件
#else
    pthread_mutex_t mutex;              // POSIX互斥锁
    pthread_cond_t queue_cond;          // 队列条件变量
#endif
} request_manager_t;

/**
 * 初始化请求管理器
 * @param config 配置参数
 * @param manager 请求管理器（输出）
 * @return 0成功，-1失败
 */
int request_manager_init(const request_manager_config_t *config, request_manager_t **manager);

/**
 * 关闭请求管理器
 * @param manager 请求管理器
 */
void request_manager_shutdown(request_manager_t *manager);

/**
 * 提交请求
 * @param manager 请求管理器
 * @param command 解析后的命令
 * @param callback 进度回调函数
 * @param user_data 用户数据
 * @return 请求ID，0表示失败
 */
uint32_t request_manager_submit(request_manager_t *manager,
                               const parsed_command_t *command,
                               progress_callback_t callback,
                               void *user_data);

/**
 * 取消请求
 * @param manager 请求管理器
 * @param request_id 请求ID
 * @return 0成功，-1失败
 */
int request_manager_cancel(request_manager_t *manager, uint32_t request_id);

/**
 * 获取请求状态
 * @param manager 请求管理器
 * @param request_id 请求ID
 * @param status 请求状态（输出）
 * @return 0成功，-1失败
 */
int request_manager_get_status(request_manager_t *manager, 
                              uint32_t request_id, 
                              request_status_t *status);

/**
 * 获取请求进度
 * @param manager 请求管理器
 * @param request_id 请求ID
 * @param progress 进度信息（输出）
 * @return 0成功，-1失败
 */
int request_manager_get_progress(request_manager_t *manager,
                                uint32_t request_id,
                                programming_progress_t *progress);

/**
 * 等待请求完成
 * @param manager 请求管理器
 * @param request_id 请求ID
 * @param timeout_ms 超时时间（毫秒），0表示无限等待
 * @return 0成功，-1失败或超时
 */
int request_manager_wait(request_manager_t *manager, 
                        uint32_t request_id, 
                        int timeout_ms);

/**
 * 列出所有请求
 * @param manager 请求管理器
 * @param requests 请求列表（输出，调用者负责释放）
 * @param count 请求数量（输出）
 * @return 0成功，-1失败
 */
int request_manager_list_requests(request_manager_t *manager,
                                 request_t ***requests,
                                 int *count);

/**
 * 获取管理器统计信息
 * @param manager 请求管理器
 * @param pending_count 待处理请求数（输出）
 * @param active_count 活动请求数（输出）
 * @param completed_count 已完成请求数（输出）
 * @return 0成功，-1失败
 */
int request_manager_get_stats(request_manager_t *manager,
                             int *pending_count,
                             int *active_count,
                             int *completed_count);

/**
 * 清理已完成的请求
 * @param manager 请求管理器
 * @return 清理的请求数量
 */
int request_manager_cleanup(request_manager_t *manager);

/**
 * 初始化请求管理器配置为默认值
 * @param config 配置结构体
 */
void init_request_manager_config(request_manager_config_t *config);

/**
 * 创建请求
 * @param command 解析后的命令
 * @param callback 进度回调函数
 * @param user_data 用户数据
 * @return 请求对象，NULL表示失败
 */
request_t* create_request(const parsed_command_t *command,
                         progress_callback_t callback,
                         void *user_data);

/**
 * 释放请求
 * @param request 请求对象
 */
void free_request(request_t *request);

/**
 * 设置请求优先级
 * @param manager 请求管理器
 * @param request_id 请求ID
 * @param priority 新的优先级
 * @return 0成功，-1失败
 */
int request_manager_set_priority(request_manager_t *manager,
                                uint32_t request_id,
                                request_priority_t priority);

#endif // REQUEST_MANAGER_H
