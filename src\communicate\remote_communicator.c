#include "remote_communicator.h"
#include "../logging/logging.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#endif

/**
 * 远程连接结构体实现
 */
struct remote_connection {
    int socket_fd;                  // 套接字文件描述符
    remote_config_t config;         // 连接配置
    int connected;                  // 连接状态
    uint32_t next_sequence_id;      // 下一个序列号
    time_t last_activity;           // 最后活动时间
    char *session_token;            // 会话令牌
};

// 协议魔数和版本
#define PROTOCOL_MAGIC 0x4A544147   // "JTAG"
#define PROTOCOL_VERSION 1

/**
 * 协议消息头结构体
 */
typedef struct {
    uint32_t magic;                 // 魔数
    uint16_t version;               // 协议版本
    uint16_t command_type;          // 命令类型
    uint32_t sequence_id;           // 序列号
    uint32_t data_length;           // 数据长度
    uint32_t checksum;              // 校验和
} protocol_header_t;

static uint32_t calculate_checksum(const void *data, size_t length) {
    const uint8_t *bytes = (const uint8_t *)data;
    uint32_t checksum = 0;
    
    for (size_t i = 0; i < length; i++) {
        checksum += bytes[i];
    }
    
    return checksum;
}

void init_remote_config(remote_config_t *config) {
    if (config == NULL) return;
    
    memset(config, 0, sizeof(remote_config_t));
    config->port = 8888;            // 默认端口
    config->timeout_seconds = 30;   // 默认超时
    config->keepalive_interval = 60; // 默认心跳间隔
    config->max_retry_count = 3;    // 默认重试次数
}

void free_remote_config(remote_config_t *config) {
    if (config == NULL) return;
    
    free(config->host);
    free(config->auth_token);
    memset(config, 0, sizeof(remote_config_t));
}

void init_remote_command(remote_command_t *command, remote_command_type_t type) {
    if (command == NULL) return;
    
    memset(command, 0, sizeof(remote_command_t));
    command->type = type;
    command->priority = 0;
    command->timeout_ms = 30000;    // 默认30秒超时
}

void free_remote_command(remote_command_t *command) {
    if (command == NULL) return;
    
    free(command->data);
    memset(command, 0, sizeof(remote_command_t));
}

void free_remote_response(remote_response_t *response) {
    if (response == NULL) return;
    
    free(response->data);
    free(response->error_message);
    memset(response, 0, sizeof(remote_response_t));
}

#ifdef _WIN32
static int init_winsock(void) {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        LOG_ERROR("WSAStartup failed: %d", result);
        return -1;
    }
    return 0;
}

static void cleanup_winsock(void) {
    WSACleanup();
}
#endif

static int create_socket_connection(const char *host, uint16_t port, int timeout_seconds) {
    int sockfd;
    struct sockaddr_in server_addr;
    struct hostent *server;
    
#ifdef _WIN32
    if (init_winsock() != 0) {
        return -1;
    }
#endif
    
    // 创建套接字
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        LOG_PERROR_ERROR("Failed to create socket");
#ifdef _WIN32
        cleanup_winsock();
#endif
        return -1;
    }
    
    // 设置超时
    struct timeval tv;
    tv.tv_sec = timeout_seconds;
    tv.tv_usec = 0;
    setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&tv, sizeof(tv));
    setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&tv, sizeof(tv));
    
    // 解析主机名
    server = gethostbyname(host);
    if (server == NULL) {
        LOG_ERROR("Failed to resolve host: %s", host);
#ifdef _WIN32
        closesocket(sockfd);
        cleanup_winsock();
#else
        close(sockfd);
#endif
        return -1;
    }
    
    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    memcpy(&server_addr.sin_addr.s_addr, server->h_addr, server->h_length);
    
    // 连接到服务器
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        LOG_PERROR_ERROR("Failed to connect to server");
#ifdef _WIN32
        closesocket(sockfd);
        cleanup_winsock();
#else
        close(sockfd);
#endif
        return -1;
    }
    
    LOG_INFO("Connected to %s:%d", host, port);
    return sockfd;
}

int remote_connect(const remote_config_t *config, remote_connection_t **connection) {
    if (config == NULL || connection == NULL) {
        LOG_ERROR("Invalid parameters for remote_connect");
        return -1;
    }
    
    if (config->host == NULL || config->port == 0) {
        LOG_ERROR("Host and port must be specified");
        return -1;
    }
    
    // 分配连接结构体
    remote_connection_t *conn = malloc(sizeof(remote_connection_t));
    if (conn == NULL) {
        LOG_ERROR("Failed to allocate memory for connection");
        return -1;
    }
    
    memset(conn, 0, sizeof(remote_connection_t));
    conn->config = *config;
    conn->next_sequence_id = 1;
    
    // 复制配置中的字符串
    if (config->host) {
        conn->config.host = strdup(config->host);
    }
    if (config->auth_token) {
        conn->config.auth_token = strdup(config->auth_token);
    }
    
    // 建立TCP连接
    conn->socket_fd = create_socket_connection(config->host, config->port, config->timeout_seconds);
    if (conn->socket_fd < 0) {
        free_remote_config(&conn->config);
        free(conn);
        return -1;
    }
    
    conn->connected = 1;
    conn->last_activity = time(NULL);
    
    *connection = conn;
    LOG_INFO("Remote connection established successfully");
    return 0;
}

void remote_disconnect(remote_connection_t *connection) {
    if (connection == NULL) return;

    if (connection->connected && connection->socket_fd >= 0) {
#ifdef _WIN32
        closesocket(connection->socket_fd);
        cleanup_winsock();
#else
        close(connection->socket_fd);
#endif
        LOG_INFO("Remote connection closed");
    }

    free_remote_config(&connection->config);
    free(connection->session_token);
    free(connection);
}

static int send_data(int sockfd, const void *data, size_t length) {
    const char *bytes = (const char *)data;
    size_t sent = 0;

    while (sent < length) {
        int result = send(sockfd, bytes + sent, length - sent, 0);
        if (result <= 0) {
            LOG_PERROR_ERROR("Failed to send data");
            return -1;
        }
        sent += result;
    }

    return 0;
}

static int receive_data(int sockfd, void *buffer, size_t length) {
    char *bytes = (char *)buffer;
    size_t received = 0;

    while (received < length) {
        int result = recv(sockfd, bytes + received, length - received, 0);
        if (result <= 0) {
            LOG_PERROR_ERROR("Failed to receive data");
            return -1;
        }
        received += result;
    }

    return 0;
}

int remote_send_command(remote_connection_t *connection,
                       const remote_command_t *command,
                       remote_response_t *response) {
    if (connection == NULL || command == NULL || response == NULL) {
        LOG_ERROR("Invalid parameters for remote_send_command");
        return -1;
    }

    if (!connection->connected) {
        LOG_ERROR("Connection is not established");
        return -1;
    }

    // 初始化响应
    memset(response, 0, sizeof(remote_response_t));

    // 构建协议头
    protocol_header_t header;
    header.magic = PROTOCOL_MAGIC;
    header.version = PROTOCOL_VERSION;
    header.command_type = command->type;
    header.sequence_id = connection->next_sequence_id++;
    header.data_length = command->data_length;
    header.checksum = command->data ? calculate_checksum(command->data, command->data_length) : 0;

    // 发送协议头
    if (send_data(connection->socket_fd, &header, sizeof(header)) != 0) {
        LOG_ERROR("Failed to send protocol header");
        return -1;
    }

    // 发送数据（如果有）
    if (command->data && command->data_length > 0) {
        if (send_data(connection->socket_fd, command->data, command->data_length) != 0) {
            LOG_ERROR("Failed to send command data");
            return -1;
        }
    }

    LOG_DEBUG("Sent command type %d, sequence %d, data length %d",
              command->type, header.sequence_id, command->data_length);

    // 接收响应头
    protocol_header_t response_header;
    if (receive_data(connection->socket_fd, &response_header, sizeof(response_header)) != 0) {
        LOG_ERROR("Failed to receive response header");
        return -1;
    }

    // 验证响应头
    if (response_header.magic != PROTOCOL_MAGIC) {
        LOG_ERROR("Invalid response magic: 0x%08x", response_header.magic);
        return -1;
    }

    if (response_header.sequence_id != header.sequence_id) {
        LOG_ERROR("Sequence ID mismatch: expected %d, got %d",
                  header.sequence_id, response_header.sequence_id);
        return -1;
    }

    response->status = (remote_status_t)response_header.command_type;
    response->sequence_id = response_header.sequence_id;
    response->data_length = response_header.data_length;

    // 接收响应数据（如果有）
    if (response->data_length > 0) {
        response->data = malloc(response->data_length);
        if (response->data == NULL) {
            LOG_ERROR("Failed to allocate memory for response data");
            return -1;
        }

        if (receive_data(connection->socket_fd, response->data, response->data_length) != 0) {
            LOG_ERROR("Failed to receive response data");
            free(response->data);
            response->data = NULL;
            return -1;
        }

        // 验证校验和
        uint32_t calculated_checksum = calculate_checksum(response->data, response->data_length);
        if (calculated_checksum != response_header.checksum) {
            LOG_ERROR("Response data checksum mismatch");
            free(response->data);
            response->data = NULL;
            return -1;
        }
    }

    connection->last_activity = time(NULL);

    LOG_DEBUG("Received response status %d, sequence %d, data length %d",
              response->status, response->sequence_id, response->data_length);

    return 0;
}

int remote_is_connected(remote_connection_t *connection) {
    if (connection == NULL) return 0;
    return connection->connected;
}

int remote_ping(remote_connection_t *connection) {
    if (connection == NULL) {
        LOG_ERROR("Connection is NULL");
        return -1;
    }

    remote_command_t command;
    init_remote_command(&command, REMOTE_CMD_PING);

    remote_response_t response;
    int ret = remote_send_command(connection, &command, &response);

    if (ret == 0 && response.status == REMOTE_STATUS_SUCCESS) {
        LOG_DEBUG("Ping successful");
    } else {
        LOG_WARNING("Ping failed");
        ret = -1;
    }

    free_remote_command(&command);
    free_remote_response(&response);
    return ret;
}

int remote_get_version(remote_connection_t *connection, char *version_buffer, size_t buffer_size) {
    if (connection == NULL || version_buffer == NULL || buffer_size == 0) {
        LOG_ERROR("Invalid parameters for remote_get_version");
        return -1;
    }

    remote_command_t command;
    init_remote_command(&command, REMOTE_CMD_GET_VERSION);

    remote_response_t response;
    int ret = remote_send_command(connection, &command, &response);

    if (ret == 0 && response.status == REMOTE_STATUS_SUCCESS && response.data) {
        size_t copy_length = (response.data_length < buffer_size - 1) ?
                            response.data_length : buffer_size - 1;
        memcpy(version_buffer, response.data, copy_length);
        version_buffer[copy_length] = '\0';
        LOG_INFO("Remote version: %s", version_buffer);
    } else {
        LOG_ERROR("Failed to get remote version");
        ret = -1;
    }

    free_remote_command(&command);
    free_remote_response(&response);
    return ret;
}

int remote_program_device(remote_connection_t *connection,
                         const remote_programming_request_t *request,
                         progress_callback_t callback,
                         void *user_data) {
    if (connection == NULL || request == NULL) {
        LOG_ERROR("Invalid parameters for remote_program_device");
        return -1;
    }

    // 序列化编程请求
    // 这里简化实现，实际应该使用更完善的序列化格式（如JSON、Protocol Buffers等）
    char *serialized_data = malloc(4096);
    if (serialized_data == NULL) {
        LOG_ERROR("Failed to allocate memory for serialized data");
        return -1;
    }

    int data_length = snprintf(serialized_data, 4096,
        "{"
        "\"firmware_file\":\"%s\","
        "\"target_address\":\"0x%08x\","
        "\"session_id\":\"%s\","
        "\"async_mode\":%d"
        "}",
        request->prog_request.firmware_file ? request->prog_request.firmware_file : "",
        request->prog_request.target_address,
        request->session_id ? request->session_id : "",
        request->async_mode
    );

    remote_command_t command;
    init_remote_command(&command, REMOTE_CMD_PROGRAM);
    command.data = serialized_data;
    command.data_length = data_length;

    remote_response_t response;
    int ret = remote_send_command(connection, &command, &response);

    if (ret == 0 && response.status == REMOTE_STATUS_SUCCESS) {
        LOG_INFO("Remote programming request sent successfully");

        // 如果是同步模式，等待完成
        if (!request->async_mode) {
            // 轮询状态直到完成
            programming_progress_t progress;
            while (1) {
                if (remote_get_programming_status(connection, request->session_id, &progress) == 0) {
                    if (callback) {
                        int result = callback(&progress, user_data);
                        if (result != 0) {
                            // 用户取消
                            remote_cancel_programming(connection, request->session_id);
                            ret = -1;
                            break;
                        }
                    }

                    if (progress.status == PROG_STATUS_SUCCESS ||
                        progress.status == PROG_STATUS_FAILED ||
                        progress.status == PROG_STATUS_CANCELLED) {
                        break;
                    }
                }

                // 等待一段时间再查询
#ifdef _WIN32
                Sleep(500);
#else
                usleep(500000);
#endif
            }
        }
    } else {
        LOG_ERROR("Failed to send remote programming request");
        ret = -1;
    }

    free_remote_command(&command);
    free_remote_response(&response);
    return ret;
}

int remote_get_programming_status(remote_connection_t *connection,
                                 const char *session_id,
                                 programming_progress_t *progress) {
    if (connection == NULL || session_id == NULL || progress == NULL) {
        LOG_ERROR("Invalid parameters for remote_get_programming_status");
        return -1;
    }

    // 构建状态查询命令
    char *query_data = malloc(256);
    if (query_data == NULL) {
        LOG_ERROR("Failed to allocate memory for query data");
        return -1;
    }

    int data_length = snprintf(query_data, 256, "{\"session_id\":\"%s\"}", session_id);

    remote_command_t command;
    init_remote_command(&command, REMOTE_CMD_STATUS);
    command.data = query_data;
    command.data_length = data_length;

    remote_response_t response;
    int ret = remote_send_command(connection, &command, &response);

    if (ret == 0 && response.status == REMOTE_STATUS_SUCCESS && response.data) {
        // 解析响应数据（简化实现）
        // 实际应该使用JSON解析器
        memset(progress, 0, sizeof(programming_progress_t));
        progress->status = PROG_STATUS_RUNNING;
        progress->progress_percent = 50; // 示例值
        progress->step_description = strdup("Remote programming in progress");

        LOG_DEBUG("Retrieved programming status for session %s", session_id);
    } else {
        LOG_ERROR("Failed to get programming status");
        ret = -1;
    }

    free_remote_command(&command);
    free_remote_response(&response);
    return ret;
}

int remote_cancel_programming(remote_connection_t *connection, const char *session_id) {
    if (connection == NULL || session_id == NULL) {
        LOG_ERROR("Invalid parameters for remote_cancel_programming");
        return -1;
    }

    char *cancel_data = malloc(256);
    if (cancel_data == NULL) {
        LOG_ERROR("Failed to allocate memory for cancel data");
        return -1;
    }

    int data_length = snprintf(cancel_data, 256, "{\"session_id\":\"%s\"}", session_id);

    remote_command_t command;
    init_remote_command(&command, REMOTE_CMD_CANCEL);
    command.data = cancel_data;
    command.data_length = data_length;

    remote_response_t response;
    int ret = remote_send_command(connection, &command, &response);

    if (ret == 0 && response.status == REMOTE_STATUS_SUCCESS) {
        LOG_INFO("Programming cancellation request sent for session %s", session_id);
    } else {
        LOG_ERROR("Failed to send cancellation request");
        ret = -1;
    }

    free_remote_command(&command);
    free_remote_response(&response);
    return ret;
}
