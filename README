# JTAG Writer 前端设计

本项目基于C语言实现JTAG烧写前端，调用OpenOCD作为后端驱动，通过JTAG接口对Flash、FPGA等介质进行烧写。支持本地和远程烧写，所有烧写命令均通过前端发送请求。

## 模块划分

1. **命令解析模块（Command Parser）**
   - 负责解析用户输入或上层系统传递的烧写命令，提取目标设备、烧写文件、操作类型等参数。

2. **请求管理模块（Request Manager）**
   - 负责管理本地和远程烧写请求，调度命令执行，支持队列和并发处理。

3. **OpenOCD接口模块（OpenOCD Interface）**
   - 封装与OpenOCD的交互，包括命令生成、进程管理、日志采集等。
   - 头文件：`src/openocd_interface/openocd_interface.h`
   - 源文件：`src/openocd_interface/openocd_interface.c`
   - 提供`build_openocd_cmd`函数生成OpenOCD命令，`run_openocd_cmd`函数执行命令并采集日志。

4. **烧写流程控制模块（Programming Controller）**
   - 负责组织烧写流程，如擦除、写入、校验等步骤，并处理异常和重试逻辑。
   - 头文件：`src/programming_controller/programming_controller.h`
   - 源文件：`src/programming_controller/programming_controller.c`
   - 提供`programming_control`函数，自动依次执行擦除、写入、校验，并在失败时重试。

5. **远程通信模块（Remote Communication）**
   - 实现前端与远程后端之间的通信协议，支持命令发送、状态查询和结果回传。
   - 头文件：`src/communicate/remote_communicator.h`
   - 源文件：`src/communicate/remote_communicator.c`
   - 提供`remote_send_command`函数，基于TCP协议发送命令到远程主机，接收响应数据。

6. **日志与错误处理模块（Logging & Error Handling）**
   - 统一记录操作日志、错误信息，便于调试和追踪。
   - 头文件：`src/logging/logging.h`
   - 源文件：`src/logging/logging.c`
   - 提供`log_message`函数支持不同级别日志输出，`log_perror`函数用于记录系统错误。

## 简要流程

1. 前端接收烧写请求（本地或远程）。
2. 命令解析模块解析请求参数。
3. 请求管理模块调度烧写任务。
4. OpenOCD接口模块与OpenOCD后端交互，执行烧写操作。
5. 烧写流程控制模块监控烧写进度和状态。
6. 日志与错误处理模块记录过程信息，并将结果反馈给用户。

## 适用场景

- 支持多种JTAG设备的烧写需求。
- 适用于本地和远程自动化烧写场景。
- 便于集成到更大规模的生产或测试系统中。

