#ifndef COMMAND_PARSER_H
#define COMMAND_PARSER_H

#include "../programming_controller/programming_controller.h"
#include "../communicate/remote_communicator.h"

/**
 * 命令类型枚举
 */
typedef enum {
    CMD_TYPE_UNKNOWN = 0,       // 未知命令
    CMD_TYPE_PROGRAM,           // 编程命令
    CMD_TYPE_ERASE,             // 擦除命令
    CMD_TYPE_VERIFY,            // 校验命令
    CMD_TYPE_READ,              // 读取命令
    CMD_TYPE_RESET,             // 复位命令
    CMD_TYPE_STATUS,            // 状态查询
    CMD_TYPE_HELP,              // 帮助命令
    CMD_TYPE_VERSION,           // 版本信息
    CMD_TYPE_LIST_INTERFACES,   // 列出接口
    CMD_TYPE_REMOTE,            // 远程命令
    CMD_TYPE_CONFIG,            // 配置命令
    CMD_TYPE_EXIT               // 退出命令
} command_type_t;

/**
 * 命令执行模式枚举
 */
typedef enum {
    EXEC_MODE_LOCAL = 0,        // 本地执行
    EXEC_MODE_REMOTE,           // 远程执行
    EXEC_MODE_AUTO              // 自动选择
} execution_mode_t;

/**
 * 解析后的命令结构体
 */
typedef struct {
    command_type_t type;                    // 命令类型
    execution_mode_t exec_mode;             // 执行模式
    programming_request_t prog_request;     // 编程请求（用于编程相关命令）
    remote_config_t remote_config;         // 远程配置（用于远程命令）
    char *output_file;                      // 输出文件路径（用于读取命令）
    char *config_file;                      // 配置文件路径
    char *session_id;                       // 会话ID（用于状态查询等）
    int verbose;                            // 详细输出标志
    int dry_run;                            // 试运行标志
    char **extra_args;                      // 额外参数
    int extra_args_count;                   // 额外参数数量
} parsed_command_t;

/**
 * 命令行参数结构体
 */
typedef struct {
    int argc;                               // 参数数量
    char **argv;                            // 参数数组
    char *input_string;                     // 输入字符串（用于交互模式）
} command_args_t;

/**
 * 解析命令行参数
 * @param args 命令行参数
 * @param command 解析后的命令（输出）
 * @return 0成功，-1失败
 */
int parse_command(const command_args_t *args, parsed_command_t *command);

/**
 * 解析交互式命令
 * @param input_line 输入行
 * @param command 解析后的命令（输出）
 * @return 0成功，-1失败
 */
int parse_interactive_command(const char *input_line, parsed_command_t *command);

/**
 * 解析配置文件
 * @param config_file 配置文件路径
 * @param command 解析后的命令（输出，会合并配置）
 * @return 0成功，-1失败
 */
int parse_config_file(const char *config_file, parsed_command_t *command);

/**
 * 验证解析后的命令
 * @param command 解析后的命令
 * @return 0有效，-1无效
 */
int validate_parsed_command(const parsed_command_t *command);

/**
 * 显示命令帮助信息
 * @param command_type 命令类型，CMD_TYPE_UNKNOWN显示所有帮助
 */
void show_command_help(command_type_t command_type);

/**
 * 显示使用说明
 */
void show_usage(void);

/**
 * 显示版本信息
 */
void show_version(void);

/**
 * 初始化解析后的命令结构体
 * @param command 命令结构体
 */
void init_parsed_command(parsed_command_t *command);

/**
 * 释放解析后的命令结构体内存
 * @param command 命令结构体
 */
void free_parsed_command(parsed_command_t *command);

/**
 * 解析JTAG接口参数
 * @param interface_str 接口字符串（格式：interface_name[:config_file][@speed]）
 * @param jtag_config JTAG配置（输出）
 * @return 0成功，-1失败
 */
int parse_jtag_interface(const char *interface_str, jtag_config_t *jtag_config);

/**
 * 解析目标设备参数
 * @param target_str 目标字符串（格式：target_name[:config_file][@address:size]）
 * @param target_config 目标配置（输出）
 * @return 0成功，-1失败
 */
int parse_target_device(const char *target_str, target_config_t *target_config);

/**
 * 解析地址参数
 * @param addr_str 地址字符串（支持十进制、十六进制）
 * @param address 地址值（输出）
 * @return 0成功，-1失败
 */
int parse_address(const char *addr_str, uint32_t *address);

/**
 * 解析大小参数
 * @param size_str 大小字符串（支持K、M后缀）
 * @param size 大小值（输出）
 * @return 0成功，-1失败
 */
int parse_size(const char *size_str, uint32_t *size);

/**
 * 解析远程主机参数
 * @param host_str 主机字符串（格式：host[:port][@token]）
 * @param remote_config 远程配置（输出）
 * @return 0成功，-1失败
 */
int parse_remote_host(const char *host_str, remote_config_t *remote_config);

/**
 * 解析编程选项参数
 * @param options_str 选项字符串（格式：option1,option2,...）
 * @param options 编程选项（输出）
 * @return 0成功，-1失败
 */
int parse_programming_options(const char *options_str, programming_options_t *options);

/**
 * 将命令转换为字符串（用于日志记录）
 * @param command 解析后的命令
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int command_to_string(const parsed_command_t *command, char *buffer, size_t buffer_size);

/**
 * 检查文件是否存在且可读
 * @param file_path 文件路径
 * @return 1存在且可读，0不存在或不可读
 */
int check_file_readable(const char *file_path);

/**
 * 检查文件是否可写
 * @param file_path 文件路径
 * @return 1可写，0不可写
 */
int check_file_writable(const char *file_path);

/**
 * 获取文件大小
 * @param file_path 文件路径
 * @return 文件大小（字节），-1表示错误
 */
long get_file_size(const char *file_path);

/**
 * 规范化文件路径
 * @param path 原始路径
 * @param normalized_path 规范化后的路径（输出）
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int normalize_file_path(const char *path, char *normalized_path, size_t buffer_size);

#endif // COMMAND_PARSER_H
