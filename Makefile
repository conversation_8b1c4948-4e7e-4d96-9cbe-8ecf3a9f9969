# JTAG Writer Frontend Makefile
# Cross-platform build system for C-based JTAG programming tool

# Project configuration
PROJECT_NAME = jtag_writer
VERSION = 1.0.0

# Compiler and tools
CC = gcc
AR = ar
STRIP = strip

# Directories
SRC_DIR = src
BUILD_DIR = build
BIN_DIR = bin
OBJ_DIR = $(BUILD_DIR)/obj
DEP_DIR = $(BUILD_DIR)/deps

# Source directories
LOGGING_DIR = $(SRC_DIR)/logging
OPENOCD_DIR = $(SRC_DIR)/openocd_interface
PROG_CTRL_DIR = $(SRC_DIR)/programming_controller
REMOTE_DIR = $(SRC_DIR)/communicate
PARSER_DIR = $(SRC_DIR)/command_parser
REQUEST_DIR = $(SRC_DIR)/request_manager

# Source files
LOGGING_SOURCES = $(LOGGING_DIR)/logging.c
OPENOCD_SOURCES = $(OPENOCD_DIR)/openocd_interface.c
PROG_CTRL_SOURCES = $(PROG_CTRL_DIR)/programming_controller.c
REMOTE_SOURCES = $(REMOTE_DIR)/remote_communicator.c
PARSER_SOURCES = $(PARSER_DIR)/command_parser.c
REQUEST_SOURCES = $(REQUEST_DIR)/request_manager.c
MAIN_SOURCES = main.c

ALL_SOURCES = $(LOGGING_SOURCES) $(OPENOCD_SOURCES) $(PROG_CTRL_SOURCES) \
              $(REMOTE_SOURCES) $(PARSER_SOURCES) $(REQUEST_SOURCES) $(MAIN_SOURCES)

# Object files
LOGGING_OBJECTS = $(patsubst $(LOGGING_DIR)/%.c,$(OBJ_DIR)/logging/%.o,$(LOGGING_SOURCES))
OPENOCD_OBJECTS = $(patsubst $(OPENOCD_DIR)/%.c,$(OBJ_DIR)/openocd_interface/%.o,$(OPENOCD_SOURCES))
PROG_CTRL_OBJECTS = $(patsubst $(PROG_CTRL_DIR)/%.c,$(OBJ_DIR)/programming_controller/%.o,$(PROG_CTRL_SOURCES))
REMOTE_OBJECTS = $(patsubst $(REMOTE_DIR)/%.c,$(OBJ_DIR)/communicate/%.o,$(REMOTE_SOURCES))
PARSER_OBJECTS = $(patsubst $(PARSER_DIR)/%.c,$(OBJ_DIR)/command_parser/%.o,$(PARSER_SOURCES))
REQUEST_OBJECTS = $(patsubst $(REQUEST_DIR)/%.c,$(OBJ_DIR)/request_manager/%.o,$(REQUEST_SOURCES))
MAIN_OBJECTS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(MAIN_SOURCES))

ALL_OBJECTS = $(LOGGING_OBJECTS) $(OPENOCD_OBJECTS) $(PROG_CTRL_OBJECTS) \
              $(REMOTE_OBJECTS) $(PARSER_OBJECTS) $(REQUEST_OBJECTS) $(MAIN_OBJECTS)

# Dependency files
DEPS = $(patsubst $(OBJ_DIR)/%.o,$(DEP_DIR)/%.d,$(ALL_OBJECTS))

# Target executable
TARGET = $(BIN_DIR)/$(PROJECT_NAME)

# Compiler flags
CFLAGS = -std=c99 -Wall -Wextra -Wpedantic -O2
CPPFLAGS = -MMD -MP -MF $(DEP_DIR)/$*.d
LDFLAGS = 
LIBS = 

# Platform-specific settings
ifeq ($(OS),Windows_NT)
    # Windows settings
    TARGET := $(TARGET).exe
    LIBS += -lws2_32
    CFLAGS += -D_WIN32_WINNT=0x0600
else
    # Unix/Linux settings
    LIBS += -lpthread
    UNAME_S := $(shell uname -s)
    ifeq ($(UNAME_S),Linux)
        CFLAGS += -D_GNU_SOURCE
    endif
    ifeq ($(UNAME_S),Darwin)
        CFLAGS += -D_DARWIN_C_SOURCE
    endif
endif

# Debug build settings
ifdef DEBUG
    CFLAGS += -g -DDEBUG -O0
    STRIP = @echo "Debug build, skipping strip"
else
    CFLAGS += -DNDEBUG
endif

# Verbose build
ifdef VERBOSE
    Q =
else
    Q = @
endif

# Default target
.PHONY: all
all: $(TARGET)

# Create directories
$(OBJ_DIR) $(DEP_DIR) $(BIN_DIR):
	$(Q)mkdir -p $@

$(OBJ_DIR)/logging $(OBJ_DIR)/openocd_interface $(OBJ_DIR)/programming_controller \
$(OBJ_DIR)/communicate $(OBJ_DIR)/command_parser $(OBJ_DIR)/request_manager \
$(DEP_DIR)/logging $(DEP_DIR)/openocd_interface $(DEP_DIR)/programming_controller \
$(DEP_DIR)/communicate $(DEP_DIR)/command_parser $(DEP_DIR)/request_manager: | $(OBJ_DIR) $(DEP_DIR)
	$(Q)mkdir -p $@

# Build target executable
$(TARGET): $(ALL_OBJECTS) | $(BIN_DIR)
	@echo "Linking $@"
	$(Q)$(CC) $(LDFLAGS) -o $@ $^ $(LIBS)
	$(Q)$(STRIP) $@

# Compile source files
$(OBJ_DIR)/logging/%.o: $(LOGGING_DIR)/%.c | $(OBJ_DIR)/logging $(DEP_DIR)/logging
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/openocd_interface/%.o: $(OPENOCD_DIR)/%.c | $(OBJ_DIR)/openocd_interface $(DEP_DIR)/openocd_interface
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/programming_controller/%.o: $(PROG_CTRL_DIR)/%.c | $(OBJ_DIR)/programming_controller $(DEP_DIR)/programming_controller
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/communicate/%.o: $(REMOTE_DIR)/%.c | $(OBJ_DIR)/communicate $(DEP_DIR)/communicate
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/command_parser/%.o: $(PARSER_DIR)/%.c | $(OBJ_DIR)/command_parser $(DEP_DIR)/command_parser
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/request_manager/%.o: $(REQUEST_DIR)/%.c | $(OBJ_DIR)/request_manager $(DEP_DIR)/request_manager
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

$(OBJ_DIR)/%.o: %.c | $(OBJ_DIR) $(DEP_DIR)
	@echo "Compiling $<"
	$(Q)$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

# Include dependency files
-include $(DEPS)

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts"
	$(Q)rm -rf $(BUILD_DIR) $(BIN_DIR)

# Install target
.PHONY: install
install: $(TARGET)
	@echo "Installing $(PROJECT_NAME)"
	$(Q)install -D $(TARGET) /usr/local/bin/$(PROJECT_NAME)

# Uninstall target
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(PROJECT_NAME)"
	$(Q)rm -f /usr/local/bin/$(PROJECT_NAME)

# Run target
.PHONY: run
run: $(TARGET)
	$(Q)$(TARGET)

# Debug target
.PHONY: debug
debug:
	$(Q)$(MAKE) DEBUG=1

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  clean    - Remove build artifacts"
	@echo "  install  - Install to /usr/local/bin"
	@echo "  uninstall- Remove from /usr/local/bin"
	@echo "  run      - Build and run the program"
	@echo "  debug    - Build with debug symbols"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Build options:"
	@echo "  DEBUG=1  - Enable debug build"
	@echo "  VERBOSE=1- Enable verbose output"

# Print build information
.PHONY: info
info:
	@echo "Project: $(PROJECT_NAME) v$(VERSION)"
	@echo "Compiler: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "LIBS: $(LIBS)"
	@echo "Target: $(TARGET)"
