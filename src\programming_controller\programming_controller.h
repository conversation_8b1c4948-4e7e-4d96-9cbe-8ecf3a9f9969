#ifndef PROGRAMMING_CONTROLLER_H
#define PROGRAMMING_CONTROLLER_H

#include "../openocd_interface/openocd_interface.h"
#include <stdint.h>

/**
 * 编程步骤枚举
 */
typedef enum {
    PROG_STEP_INIT = 0,         // 初始化
    PROG_STEP_CONNECT,          // 连接目标
    PROG_STEP_ERASE,            // 擦除
    PROG_STEP_WRITE,            // 写入
    PROG_STEP_VERIFY,           // 校验
    PROG_STEP_RESET,            // 复位
    PROG_STEP_COMPLETE,         // 完成
    PROG_STEP_ERROR             // 错误
} programming_step_t;

/**
 * 编程状态枚举
 */
typedef enum {
    PROG_STATUS_IDLE = 0,       // 空闲
    PROG_STATUS_RUNNING,        // 运行中
    PROG_STATUS_SUCCESS,        // 成功
    PROG_STATUS_FAILED,         // 失败
    PROG_STATUS_CANCELLED       // 已取消
} programming_status_t;

/**
 * 编程选项结构体
 */
typedef struct {
    int enable_erase;           // 是否启用擦除
    int enable_verify;          // 是否启用校验
    int enable_reset;           // 是否启用复位
    int auto_retry;             // 是否自动重试
    int max_retry_count;        // 最大重试次数
    int retry_delay_ms;         // 重试延迟（毫秒）
    int progress_callback_interval; // 进度回调间隔（毫秒）
} programming_options_t;

/**
 * 编程进度信息结构体
 */
typedef struct {
    programming_step_t current_step;    // 当前步骤
    programming_status_t status;        // 当前状态
    int progress_percent;               // 进度百分比 (0-100)
    char *step_description;             // 步骤描述
    char *error_message;                // 错误消息
    int elapsed_time_ms;                // 已用时间（毫秒）
    int estimated_remaining_ms;         // 预计剩余时间（毫秒）
} programming_progress_t;

/**
 * 编程请求结构体
 */
typedef struct {
    jtag_config_t jtag_config;          // JTAG配置
    target_config_t target_config;      // 目标配置
    char *firmware_file;                // 固件文件路径
    uint32_t target_address;            // 目标地址
    programming_options_t options;      // 编程选项
    void *user_data;                    // 用户数据
} programming_request_t;

/**
 * 进度回调函数类型
 * @param progress 进度信息
 * @param user_data 用户数据
 * @return 0继续，非0取消
 */
typedef int (*progress_callback_t)(const programming_progress_t *progress, void *user_data);

/**
 * 编程控制器上下文结构体
 */
typedef struct {
    programming_request_t request;      // 编程请求
    programming_progress_t progress;    // 进度信息
    progress_callback_t callback;       // 进度回调函数
    int cancelled;                      // 是否已取消
    int retry_count;                    // 当前重试次数
} programming_context_t;

/**
 * 主要编程控制函数
 * 自动依次执行擦除、写入、校验，并在失败时重试
 * @param request 编程请求
 * @param callback 进度回调函数（可为NULL）
 * @return 0成功，-1失败
 */
int programming_control(const programming_request_t *request, progress_callback_t callback);

/**
 * 异步编程控制函数
 * @param request 编程请求
 * @param callback 进度回调函数（可为NULL）
 * @param context 编程上下文（用于控制和查询状态）
 * @return 0成功启动，-1失败
 */
int programming_control_async(const programming_request_t *request, 
                             progress_callback_t callback,
                             programming_context_t **context);

/**
 * 取消编程操作
 * @param context 编程上下文
 * @return 0成功，-1失败
 */
int programming_cancel(programming_context_t *context);

/**
 * 获取编程进度
 * @param context 编程上下文
 * @param progress 进度信息（输出）
 * @return 0成功，-1失败
 */
int programming_get_progress(programming_context_t *context, programming_progress_t *progress);

/**
 * 等待编程完成
 * @param context 编程上下文
 * @param timeout_ms 超时时间（毫秒），0表示无限等待
 * @return 0成功，-1失败或超时
 */
int programming_wait_complete(programming_context_t *context, int timeout_ms);

/**
 * 释放编程上下文
 * @param context 编程上下文
 */
void programming_free_context(programming_context_t *context);

/**
 * 初始化编程请求结构体
 * @param request 编程请求
 */
void init_programming_request(programming_request_t *request);

/**
 * 释放编程请求结构体内存
 * @param request 编程请求
 */
void free_programming_request(programming_request_t *request);

/**
 * 初始化编程选项为默认值
 * @param options 编程选项
 */
void init_programming_options(programming_options_t *options);

/**
 * 验证编程请求参数
 * @param request 编程请求
 * @return 0有效，-1无效
 */
int validate_programming_request(const programming_request_t *request);

/**
 * 估算编程时间
 * @param request 编程请求
 * @return 预计时间（毫秒），-1表示无法估算
 */
int estimate_programming_time(const programming_request_t *request);

#endif // PROGRAMMING_CONTROLLER_H
