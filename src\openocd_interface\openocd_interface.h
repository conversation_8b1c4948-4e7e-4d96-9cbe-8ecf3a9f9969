#ifndef OPENOCD_INTERFACE_H
#define OPENOCD_INTERFACE_H

#include <stdio.h>
#include <stdint.h>

/**
 * OpenOCD操作类型枚举
 */
typedef enum {
    OPENOCD_OP_ERASE = 0,       // 擦除操作
    OPENOCD_OP_WRITE,           // 写入操作
    OPENOCD_OP_VERIFY,          // 校验操作
    OPENOCD_OP_READ,            // 读取操作
    OPENOCD_OP_RESET,           // 复位操作
    OPENOCD_OP_HALT,            // 停止操作
    OPENOCD_OP_RESUME           // 恢复操作
} openocd_operation_t;

/**
 * 目标设备类型枚举
 */
typedef enum {
    TARGET_TYPE_FLASH = 0,      // Flash存储器
    TARGET_TYPE_FPGA,           // FPGA设备
    TARGET_TYPE_MCU,            // 微控制器
    TARGET_TYPE_CUSTOM          // 自定义设备
} target_type_t;

/**
 * JTAG接口配置结构体
 */
typedef struct {
    char *interface_name;       // 接口名称 (如 "ft2232", "jlink" 等)
    char *interface_config;     // 接口配置文件路径
    uint32_t speed_khz;         // JTAG时钟频率 (kHz)
    char *transport;            // 传输协议 ("jtag", "swd" 等)
} jtag_config_t;

/**
 * 目标设备配置结构体
 */
typedef struct {
    target_type_t type;         // 设备类型
    char *target_config;        // 目标配置文件路径
    char *chip_name;            // 芯片名称
    uint32_t base_address;      // 基地址
    uint32_t size;              // 存储器大小
} target_config_t;

/**
 * OpenOCD命令参数结构体
 */
typedef struct {
    openocd_operation_t operation;  // 操作类型
    jtag_config_t jtag_config;      // JTAG配置
    target_config_t target_config;  // 目标配置
    char *file_path;                // 文件路径（用于读写操作）
    uint32_t address;               // 操作地址
    uint32_t length;                // 操作长度
    char *extra_args;               // 额外参数
    int timeout_seconds;            // 超时时间（秒）
} openocd_cmd_params_t;

/**
 * OpenOCD执行结果结构体
 */
typedef struct {
    int exit_code;              // 退出码
    char *stdout_output;        // 标准输出
    char *stderr_output;        // 标准错误输出
    int execution_time_ms;      // 执行时间（毫秒）
    int timed_out;              // 是否超时
} openocd_result_t;

/**
 * 构建OpenOCD命令字符串
 * @param params 命令参数
 * @param cmd_buffer 命令缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int build_openocd_cmd(const openocd_cmd_params_t *params, char *cmd_buffer, size_t buffer_size);

/**
 * 执行OpenOCD命令并采集日志
 * @param cmd_string 命令字符串
 * @param result 执行结果（调用者负责释放内存）
 * @return 0成功，-1失败
 */
int run_openocd_cmd(const char *cmd_string, openocd_result_t *result);

/**
 * 释放OpenOCD执行结果内存
 * @param result 执行结果
 */
void free_openocd_result(openocd_result_t *result);

/**
 * 初始化OpenOCD命令参数结构体
 * @param params 参数结构体
 */
void init_openocd_params(openocd_cmd_params_t *params);

/**
 * 释放OpenOCD命令参数结构体内存
 * @param params 参数结构体
 */
void free_openocd_params(openocd_cmd_params_t *params);

/**
 * 检查OpenOCD是否可用
 * @return 1可用，0不可用
 */
int check_openocd_available(void);

/**
 * 获取OpenOCD版本信息
 * @param version_buffer 版本信息缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int get_openocd_version(char *version_buffer, size_t buffer_size);

/**
 * 验证JTAG连接
 * @param jtag_config JTAG配置
 * @return 0成功，-1失败
 */
int verify_jtag_connection(const jtag_config_t *jtag_config);

#endif // OPENOCD_INTERFACE_H
