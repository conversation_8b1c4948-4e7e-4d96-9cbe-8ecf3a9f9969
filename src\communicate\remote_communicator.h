#ifndef REMOTE_COMMUNICATOR_H
#define REMOTE_COMMUNICATOR_H

#include "../programming_controller/programming_controller.h"
#include <stdint.h>

/**
 * 远程命令类型枚举
 */
typedef enum {
    REMOTE_CMD_PING = 0,            // 心跳检测
    REMOTE_CMD_PROGRAM,             // 编程命令
    REMOTE_CMD_STATUS,              // 状态查询
    REMOTE_CMD_CANCEL,              // 取消操作
    REMOTE_CMD_GET_VERSION,         // 获取版本信息
    REMOTE_CMD_LIST_INTERFACES,     // 列出可用接口
    REMOTE_CMD_CUSTOM               // 自定义命令
} remote_command_type_t;

/**
 * 远程响应状态枚举
 */
typedef enum {
    REMOTE_STATUS_SUCCESS = 0,      // 成功
    REMOTE_STATUS_ERROR,            // 错误
    REMOTE_STATUS_BUSY,             // 忙碌
    REMOTE_STATUS_TIMEOUT,          // 超时
    REMOTE_STATUS_INVALID_CMD,      // 无效命令
    REMOTE_STATUS_CONNECTION_LOST   // 连接丢失
} remote_status_t;

/**
 * 远程连接配置结构体
 */
typedef struct {
    char *host;                     // 主机地址
    uint16_t port;                  // 端口号
    int timeout_seconds;            // 连接超时时间
    int keepalive_interval;         // 心跳间隔（秒）
    int max_retry_count;            // 最大重试次数
    char *auth_token;               // 认证令牌
} remote_config_t;

/**
 * 远程命令结构体
 */
typedef struct {
    remote_command_type_t type;     // 命令类型
    uint32_t sequence_id;           // 序列号
    uint32_t data_length;           // 数据长度
    void *data;                     // 命令数据
    int priority;                   // 优先级
    int timeout_ms;                 // 超时时间（毫秒）
} remote_command_t;

/**
 * 远程响应结构体
 */
typedef struct {
    remote_status_t status;         // 响应状态
    uint32_t sequence_id;           // 对应的序列号
    uint32_t data_length;           // 响应数据长度
    void *data;                     // 响应数据
    char *error_message;            // 错误消息
    int execution_time_ms;          // 执行时间（毫秒）
} remote_response_t;

/**
 * 远程编程请求结构体
 */
typedef struct {
    programming_request_t prog_request; // 编程请求
    char *session_id;               // 会话ID
    int async_mode;                 // 是否异步模式
} remote_programming_request_t;

/**
 * 远程连接句柄
 */
typedef struct remote_connection remote_connection_t;

/**
 * 远程事件回调函数类型
 * @param event_type 事件类型
 * @param data 事件数据
 * @param user_data 用户数据
 */
typedef void (*remote_event_callback_t)(const char *event_type, const void *data, void *user_data);

/**
 * 建立远程连接
 * @param config 连接配置
 * @param connection 连接句柄（输出）
 * @return 0成功，-1失败
 */
int remote_connect(const remote_config_t *config, remote_connection_t **connection);

/**
 * 关闭远程连接
 * @param connection 连接句柄
 */
void remote_disconnect(remote_connection_t *connection);

/**
 * 发送远程命令
 * @param connection 连接句柄
 * @param command 命令
 * @param response 响应（输出，调用者负责释放）
 * @return 0成功，-1失败
 */
int remote_send_command(remote_connection_t *connection, 
                       const remote_command_t *command, 
                       remote_response_t *response);

/**
 * 异步发送远程命令
 * @param connection 连接句柄
 * @param command 命令
 * @param callback 完成回调函数
 * @param user_data 用户数据
 * @return 命令序列号，-1表示失败
 */
int remote_send_command_async(remote_connection_t *connection,
                             const remote_command_t *command,
                             remote_event_callback_t callback,
                             void *user_data);

/**
 * 发送编程请求到远程主机
 * @param connection 连接句柄
 * @param request 编程请求
 * @param callback 进度回调函数
 * @param user_data 用户数据
 * @return 0成功，-1失败
 */
int remote_program_device(remote_connection_t *connection,
                         const remote_programming_request_t *request,
                         progress_callback_t callback,
                         void *user_data);

/**
 * 查询远程编程状态
 * @param connection 连接句柄
 * @param session_id 会话ID
 * @param progress 进度信息（输出）
 * @return 0成功，-1失败
 */
int remote_get_programming_status(remote_connection_t *connection,
                                 const char *session_id,
                                 programming_progress_t *progress);

/**
 * 取消远程编程操作
 * @param connection 连接句柄
 * @param session_id 会话ID
 * @return 0成功，-1失败
 */
int remote_cancel_programming(remote_connection_t *connection, const char *session_id);

/**
 * 检查远程连接状态
 * @param connection 连接句柄
 * @return 1连接正常，0连接断开
 */
int remote_is_connected(remote_connection_t *connection);

/**
 * 发送心跳包
 * @param connection 连接句柄
 * @return 0成功，-1失败
 */
int remote_ping(remote_connection_t *connection);

/**
 * 获取远程主机版本信息
 * @param connection 连接句柄
 * @param version_buffer 版本信息缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int remote_get_version(remote_connection_t *connection, char *version_buffer, size_t buffer_size);

/**
 * 列出远程主机可用的JTAG接口
 * @param connection 连接句柄
 * @param interfaces 接口列表（输出，调用者负责释放）
 * @param count 接口数量（输出）
 * @return 0成功，-1失败
 */
int remote_list_interfaces(remote_connection_t *connection, char ***interfaces, int *count);

/**
 * 初始化远程配置为默认值
 * @param config 远程配置
 */
void init_remote_config(remote_config_t *config);

/**
 * 释放远程配置内存
 * @param config 远程配置
 */
void free_remote_config(remote_config_t *config);

/**
 * 释放远程响应内存
 * @param response 远程响应
 */
void free_remote_response(remote_response_t *response);

/**
 * 初始化远程命令
 * @param command 远程命令
 * @param type 命令类型
 */
void init_remote_command(remote_command_t *command, remote_command_type_t type);

/**
 * 释放远程命令内存
 * @param command 远程命令
 */
void free_remote_command(remote_command_t *command);

#endif // REMOTE_COMMUNICATOR_H
