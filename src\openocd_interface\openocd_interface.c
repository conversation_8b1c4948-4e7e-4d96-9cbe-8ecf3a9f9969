#include "openocd_interface.h"
#include "../logging/logging.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

#ifdef _WIN32
#include <windows.h>
#include <process.h>
#else
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>
#endif

// 操作类型字符串映射
static const char* operation_strings[] = {
    "flash erase_sector",
    "flash write_image",
    "flash verify_image", 
    "flash read_bank",
    "reset",
    "halt",
    "resume"
};

void init_openocd_params(openocd_cmd_params_t *params) {
    if (params == NULL) return;
    
    memset(params, 0, sizeof(openocd_cmd_params_t));
    params->operation = OPENOCD_OP_WRITE;
    params->jtag_config.speed_khz = 1000;  // 默认1MHz
    params->timeout_seconds = 30;          // 默认30秒超时
}

void free_openocd_params(openocd_cmd_params_t *params) {
    if (params == NULL) return;
    
    free(params->jtag_config.interface_name);
    free(params->jtag_config.interface_config);
    free(params->jtag_config.transport);
    free(params->target_config.target_config);
    free(params->target_config.chip_name);
    free(params->file_path);
    free(params->extra_args);
    
    memset(params, 0, sizeof(openocd_cmd_params_t));
}

void free_openocd_result(openocd_result_t *result) {
    if (result == NULL) return;
    
    free(result->stdout_output);
    free(result->stderr_output);
    memset(result, 0, sizeof(openocd_result_t));
}

int build_openocd_cmd(const openocd_cmd_params_t *params, char *cmd_buffer, size_t buffer_size) {
    if (params == NULL || cmd_buffer == NULL || buffer_size == 0) {
        LOG_ERROR("Invalid parameters for build_openocd_cmd");
        return -1;
    }
    
    // 开始构建命令
    int written = snprintf(cmd_buffer, buffer_size, "openocd");
    if (written >= buffer_size) {
        LOG_ERROR("Command buffer too small");
        return -1;
    }
    
    // 添加接口配置
    if (params->jtag_config.interface_name) {
        written += snprintf(cmd_buffer + written, buffer_size - written, 
                           " -f interface/%s.cfg", params->jtag_config.interface_name);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for interface");
            return -1;
        }
    }
    
    // 添加自定义接口配置文件
    if (params->jtag_config.interface_config) {
        written += snprintf(cmd_buffer + written, buffer_size - written,
                           " -f \"%s\"", params->jtag_config.interface_config);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for interface config");
            return -1;
        }
    }
    
    // 添加目标配置
    if (params->target_config.target_config) {
        written += snprintf(cmd_buffer + written, buffer_size - written,
                           " -f \"%s\"", params->target_config.target_config);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for target config");
            return -1;
        }
    }
    
    // 添加JTAG速度设置
    if (params->jtag_config.speed_khz > 0) {
        written += snprintf(cmd_buffer + written, buffer_size - written,
                           " -c \"adapter speed %u\"", params->jtag_config.speed_khz);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for speed setting");
            return -1;
        }
    }
    
    // 添加传输协议
    if (params->jtag_config.transport) {
        written += snprintf(cmd_buffer + written, buffer_size - written,
                           " -c \"transport select %s\"", params->jtag_config.transport);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for transport");
            return -1;
        }
    }
    
    // 添加初始化命令
    written += snprintf(cmd_buffer + written, buffer_size - written, " -c \"init\"");
    if (written >= buffer_size) {
        LOG_ERROR("Command buffer too small for init");
        return -1;
    }
    
    // 根据操作类型添加具体命令
    switch (params->operation) {
        case OPENOCD_OP_ERASE:
            if (params->target_config.type == TARGET_TYPE_FLASH) {
                written += snprintf(cmd_buffer + written, buffer_size - written,
                                   " -c \"flash erase_sector 0 0 last\"");
            }
            break;
            
        case OPENOCD_OP_WRITE:
            if (params->file_path) {
                written += snprintf(cmd_buffer + written, buffer_size - written,
                                   " -c \"flash write_image erase \\\"%s\\\" 0x%08x\"",
                                   params->file_path, params->address);
            }
            break;
            
        case OPENOCD_OP_VERIFY:
            if (params->file_path) {
                written += snprintf(cmd_buffer + written, buffer_size - written,
                                   " -c \"flash verify_image \\\"%s\\\" 0x%08x\"",
                                   params->file_path, params->address);
            }
            break;
            
        case OPENOCD_OP_READ:
            if (params->file_path) {
                written += snprintf(cmd_buffer + written, buffer_size - written,
                                   " -c \"flash read_bank 0 \\\"%s\\\" 0x%08x %u\"",
                                   params->file_path, params->address, params->length);
            }
            break;
            
        case OPENOCD_OP_RESET:
            written += snprintf(cmd_buffer + written, buffer_size - written,
                               " -c \"reset\"");
            break;
            
        case OPENOCD_OP_HALT:
            written += snprintf(cmd_buffer + written, buffer_size - written,
                               " -c \"halt\"");
            break;
            
        case OPENOCD_OP_RESUME:
            written += snprintf(cmd_buffer + written, buffer_size - written,
                               " -c \"resume\"");
            break;
            
        default:
            LOG_ERROR("Unknown operation type: %d", params->operation);
            return -1;
    }
    
    if (written >= buffer_size) {
        LOG_ERROR("Command buffer too small for operation");
        return -1;
    }
    
    // 添加额外参数
    if (params->extra_args) {
        written += snprintf(cmd_buffer + written, buffer_size - written,
                           " %s", params->extra_args);
        if (written >= buffer_size) {
            LOG_ERROR("Command buffer too small for extra args");
            return -1;
        }
    }
    
    // 添加退出命令
    written += snprintf(cmd_buffer + written, buffer_size - written, " -c \"exit\"");
    if (written >= buffer_size) {
        LOG_ERROR("Command buffer too small for exit");
        return -1;
    }
    
    LOG_DEBUG("Built OpenOCD command: %s", cmd_buffer);
    return 0;
}

#ifdef _WIN32
static int execute_command_windows(const char *cmd_string, openocd_result_t *result, int timeout_seconds) {
    HANDLE hStdoutRead, hStdoutWrite;
    HANDLE hStderrRead, hStderrWrite;
    SECURITY_ATTRIBUTES sa;
    PROCESS_INFORMATION pi;
    STARTUPINFO si;

    // 设置安全属性
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.bInheritHandle = TRUE;
    sa.lpSecurityDescriptor = NULL;

    // 创建管道
    if (!CreatePipe(&hStdoutRead, &hStdoutWrite, &sa, 0) ||
        !CreatePipe(&hStderrRead, &hStderrWrite, &sa, 0)) {
        LOG_PERROR_ERROR("Failed to create pipes");
        return -1;
    }

    // 设置启动信息
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    si.hStdError = hStderrWrite;
    si.hStdOutput = hStdoutWrite;
    si.dwFlags |= STARTF_USESTDHANDLES;

    // 创建进程
    if (!CreateProcess(NULL, (LPSTR)cmd_string, NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi)) {
        LOG_PERROR_ERROR("Failed to create process");
        CloseHandle(hStdoutRead);
        CloseHandle(hStdoutWrite);
        CloseHandle(hStderrRead);
        CloseHandle(hStderrWrite);
        return -1;
    }

    // 关闭写端
    CloseHandle(hStdoutWrite);
    CloseHandle(hStderrWrite);

    // 等待进程完成或超时
    DWORD wait_result = WaitForSingleObject(pi.hProcess, timeout_seconds * 1000);

    if (wait_result == WAIT_TIMEOUT) {
        LOG_WARNING("OpenOCD command timed out, terminating process");
        TerminateProcess(pi.hProcess, 1);
        result->timed_out = 1;
        result->exit_code = 1;
    } else {
        GetExitCodeProcess(pi.hProcess, (DWORD*)&result->exit_code);
        result->timed_out = 0;
    }

    // 读取输出
    DWORD bytes_read;
    char buffer[4096];
    size_t stdout_size = 0, stderr_size = 0;

    // 读取标准输出
    result->stdout_output = malloc(1);
    result->stdout_output[0] = '\0';

    while (ReadFile(hStdoutRead, buffer, sizeof(buffer) - 1, &bytes_read, NULL) && bytes_read > 0) {
        buffer[bytes_read] = '\0';
        result->stdout_output = realloc(result->stdout_output, stdout_size + bytes_read + 1);
        strcat(result->stdout_output, buffer);
        stdout_size += bytes_read;
    }

    // 读取标准错误
    result->stderr_output = malloc(1);
    result->stderr_output[0] = '\0';

    while (ReadFile(hStderrRead, buffer, sizeof(buffer) - 1, &bytes_read, NULL) && bytes_read > 0) {
        buffer[bytes_read] = '\0';
        result->stderr_output = realloc(result->stderr_output, stderr_size + bytes_read + 1);
        strcat(result->stderr_output, buffer);
        stderr_size += bytes_read;
    }

    // 清理资源
    CloseHandle(hStdoutRead);
    CloseHandle(hStderrRead);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return 0;
}
#else
static int execute_command_unix(const char *cmd_string, openocd_result_t *result, int timeout_seconds) {
    // Unix/Linux implementation would go here
    // For now, return error on non-Windows platforms
    LOG_ERROR("Unix implementation not yet available");
    return -1;
}
#endif

int run_openocd_cmd(const char *cmd_string, openocd_result_t *result) {
    if (cmd_string == NULL || result == NULL) {
        LOG_ERROR("Invalid parameters for run_openocd_cmd");
        return -1;
    }

    // 初始化结果结构体
    memset(result, 0, sizeof(openocd_result_t));

    clock_t start_time = clock();

    LOG_INFO("Executing OpenOCD command: %s", cmd_string);

#ifdef _WIN32
    int ret = execute_command_windows(cmd_string, result, 30);
#else
    int ret = execute_command_unix(cmd_string, result, 30);
#endif

    clock_t end_time = clock();
    result->execution_time_ms = (int)((end_time - start_time) * 1000 / CLOCKS_PER_SEC);

    if (ret == 0) {
        LOG_INFO("OpenOCD command completed in %d ms, exit code: %d",
                 result->execution_time_ms, result->exit_code);

        if (result->stdout_output && strlen(result->stdout_output) > 0) {
            LOG_DEBUG("OpenOCD stdout: %s", result->stdout_output);
        }

        if (result->stderr_output && strlen(result->stderr_output) > 0) {
            LOG_DEBUG("OpenOCD stderr: %s", result->stderr_output);
        }
    } else {
        LOG_ERROR("Failed to execute OpenOCD command");
    }

    return ret;
}

int check_openocd_available(void) {
    openocd_result_t result;
    int ret = run_openocd_cmd("openocd --version", &result);

    if (ret == 0 && result.exit_code == 0) {
        free_openocd_result(&result);
        return 1;
    }

    free_openocd_result(&result);
    return 0;
}

int get_openocd_version(char *version_buffer, size_t buffer_size) {
    if (version_buffer == NULL || buffer_size == 0) {
        return -1;
    }

    openocd_result_t result;
    int ret = run_openocd_cmd("openocd --version", &result);

    if (ret == 0 && result.exit_code == 0 && result.stdout_output) {
        strncpy(version_buffer, result.stdout_output, buffer_size - 1);
        version_buffer[buffer_size - 1] = '\0';
        free_openocd_result(&result);
        return 0;
    }

    free_openocd_result(&result);
    return -1;
}

int verify_jtag_connection(const jtag_config_t *jtag_config) {
    if (jtag_config == NULL) {
        LOG_ERROR("Invalid JTAG configuration");
        return -1;
    }

    openocd_cmd_params_t params;
    init_openocd_params(&params);

    // 设置JTAG配置
    if (jtag_config->interface_name) {
        params.jtag_config.interface_name = strdup(jtag_config->interface_name);
    }
    if (jtag_config->interface_config) {
        params.jtag_config.interface_config = strdup(jtag_config->interface_config);
    }
    if (jtag_config->transport) {
        params.jtag_config.transport = strdup(jtag_config->transport);
    }
    params.jtag_config.speed_khz = jtag_config->speed_khz;

    // 设置简单的连接测试操作
    params.operation = OPENOCD_OP_HALT;
    params.timeout_seconds = 10;

    char cmd_buffer[2048];
    int ret = build_openocd_cmd(&params, cmd_buffer, sizeof(cmd_buffer));

    if (ret == 0) {
        openocd_result_t result;
        ret = run_openocd_cmd(cmd_buffer, &result);

        if (ret == 0 && result.exit_code == 0) {
            LOG_INFO("JTAG connection verified successfully");
        } else {
            LOG_ERROR("JTAG connection verification failed");
            ret = -1;
        }

        free_openocd_result(&result);
    }

    free_openocd_params(&params);
    return ret;
}
