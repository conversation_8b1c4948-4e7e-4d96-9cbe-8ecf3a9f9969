#include "command_parser.h"
#include "../logging/logging.h"
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <sys/stat.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// 版本信息
#define JTAG_WRITER_VERSION "1.0.0"
#define JTAG_WRITER_BUILD_DATE __DATE__

// 命令字符串映射
static const struct {
    const char *name;
    command_type_t type;
} command_map[] = {
    {"program", CMD_TYPE_PROGRAM},
    {"prog", CMD_TYPE_PROGRAM},
    {"write", CMD_TYPE_PROGRAM},
    {"erase", CMD_TYPE_ERASE},
    {"verify", CMD_TYPE_VERIFY},
    {"read", CMD_TYPE_READ},
    {"reset", CMD_TYPE_RESET},
    {"status", CMD_TYPE_STATUS},
    {"help", C<PERSON>_TYPE_HELP},
    {"version", CMD_TYPE_VERSION},
    {"list", CMD_TYPE_LIST_INTERFACES},
    {"remote", CMD_TYPE_REMOTE},
    {"config", CMD_TYPE_CONFIG},
    {"exit", CMD_TYPE_EXIT},
    {"quit", CMD_TYPE_EXIT},
    {NULL, CMD_TYPE_UNKNOWN}
};

void init_parsed_command(parsed_command_t *command) {
    if (command == NULL) return;
    
    memset(command, 0, sizeof(parsed_command_t));
    command->type = CMD_TYPE_UNKNOWN;
    command->exec_mode = EXEC_MODE_LOCAL;
    
    init_programming_request(&command->prog_request);
    init_remote_config(&command->remote_config);
}

void free_parsed_command(parsed_command_t *command) {
    if (command == NULL) return;
    
    free_programming_request(&command->prog_request);
    free_remote_config(&command->remote_config);
    
    free(command->output_file);
    free(command->config_file);
    free(command->session_id);
    
    if (command->extra_args) {
        for (int i = 0; i < command->extra_args_count; i++) {
            free(command->extra_args[i]);
        }
        free(command->extra_args);
    }
    
    memset(command, 0, sizeof(parsed_command_t));
}

static command_type_t parse_command_type(const char *cmd_str) {
    if (cmd_str == NULL) return CMD_TYPE_UNKNOWN;
    
    for (int i = 0; command_map[i].name != NULL; i++) {
        if (strcasecmp(cmd_str, command_map[i].name) == 0) {
            return command_map[i].type;
        }
    }
    
    return CMD_TYPE_UNKNOWN;
}

int parse_address(const char *addr_str, uint32_t *address) {
    if (addr_str == NULL || address == NULL) {
        return -1;
    }
    
    char *endptr;
    unsigned long value;
    
    // 检查是否为十六进制格式
    if (strncasecmp(addr_str, "0x", 2) == 0) {
        value = strtoul(addr_str, &endptr, 16);
    } else {
        value = strtoul(addr_str, &endptr, 10);
    }
    
    if (*endptr != '\0' || value > UINT32_MAX) {
        LOG_ERROR("Invalid address format: %s", addr_str);
        return -1;
    }
    
    *address = (uint32_t)value;
    return 0;
}

int parse_size(const char *size_str, uint32_t *size) {
    if (size_str == NULL || size == NULL) {
        return -1;
    }
    
    char *endptr;
    unsigned long value = strtoul(size_str, &endptr, 10);
    
    if (value > UINT32_MAX) {
        LOG_ERROR("Size value too large: %s", size_str);
        return -1;
    }
    
    // 处理后缀
    if (*endptr != '\0') {
        switch (toupper(*endptr)) {
            case 'K':
                value *= 1024;
                break;
            case 'M':
                value *= 1024 * 1024;
                break;
            case 'G':
                value *= 1024 * 1024 * 1024;
                break;
            default:
                LOG_ERROR("Invalid size suffix: %c", *endptr);
                return -1;
        }
        
        if (value > UINT32_MAX) {
            LOG_ERROR("Size value too large after suffix: %s", size_str);
            return -1;
        }
    }
    
    *size = (uint32_t)value;
    return 0;
}

int parse_jtag_interface(const char *interface_str, jtag_config_t *jtag_config) {
    if (interface_str == NULL || jtag_config == NULL) {
        return -1;
    }
    
    char *str_copy = strdup(interface_str);
    if (str_copy == NULL) {
        return -1;
    }
    
    // 解析格式：interface_name[:config_file][@speed]
    char *interface_name = str_copy;
    char *config_file = NULL;
    char *speed_str = NULL;
    
    // 查找配置文件分隔符
    char *colon = strchr(str_copy, ':');
    if (colon != NULL) {
        *colon = '\0';
        config_file = colon + 1;
        
        // 查找速度分隔符
        char *at = strchr(config_file, '@');
        if (at != NULL) {
            *at = '\0';
            speed_str = at + 1;
        }
    } else {
        // 查找速度分隔符
        char *at = strchr(str_copy, '@');
        if (at != NULL) {
            *at = '\0';
            speed_str = at + 1;
        }
    }
    
    // 设置接口名称
    jtag_config->interface_name = strdup(interface_name);
    
    // 设置配置文件
    if (config_file && strlen(config_file) > 0) {
        jtag_config->interface_config = strdup(config_file);
    }
    
    // 设置速度
    if (speed_str && strlen(speed_str) > 0) {
        uint32_t speed;
        if (parse_size(speed_str, &speed) == 0) {
            jtag_config->speed_khz = speed;
        } else {
            LOG_ERROR("Invalid JTAG speed: %s", speed_str);
            free(str_copy);
            return -1;
        }
    } else {
        jtag_config->speed_khz = 1000; // 默认1MHz
    }
    
    // 设置默认传输协议
    jtag_config->transport = strdup("jtag");
    
    free(str_copy);
    return 0;
}

int parse_target_device(const char *target_str, target_config_t *target_config) {
    if (target_str == NULL || target_config == NULL) {
        return -1;
    }
    
    char *str_copy = strdup(target_str);
    if (str_copy == NULL) {
        return -1;
    }
    
    // 解析格式：target_name[:config_file][@address:size]
    char *target_name = str_copy;
    char *config_file = NULL;
    char *address_size = NULL;
    
    // 查找配置文件分隔符
    char *colon = strchr(str_copy, ':');
    if (colon != NULL) {
        *colon = '\0';
        config_file = colon + 1;
        
        // 查找地址分隔符
        char *at = strchr(config_file, '@');
        if (at != NULL) {
            *at = '\0';
            address_size = at + 1;
        }
    } else {
        // 查找地址分隔符
        char *at = strchr(str_copy, '@');
        if (at != NULL) {
            *at = '\0';
            address_size = at + 1;
        }
    }
    
    // 设置目标名称
    target_config->chip_name = strdup(target_name);
    
    // 设置配置文件
    if (config_file && strlen(config_file) > 0) {
        target_config->target_config = strdup(config_file);
    }
    
    // 解析地址和大小
    if (address_size && strlen(address_size) > 0) {
        char *size_part = strchr(address_size, ':');
        if (size_part != NULL) {
            *size_part = '\0';
            size_part++;
            
            if (parse_address(address_size, &target_config->base_address) != 0 ||
                parse_size(size_part, &target_config->size) != 0) {
                LOG_ERROR("Invalid address:size format: %s", address_size);
                free(str_copy);
                return -1;
            }
        } else {
            if (parse_address(address_size, &target_config->base_address) != 0) {
                LOG_ERROR("Invalid address format: %s", address_size);
                free(str_copy);
                return -1;
            }
        }
    }
    
    // 设置默认类型
    target_config->type = TARGET_TYPE_FLASH;
    
    free(str_copy);
    return 0;
}

int parse_remote_host(const char *host_str, remote_config_t *remote_config) {
    if (host_str == NULL || remote_config == NULL) {
        return -1;
    }

    char *str_copy = strdup(host_str);
    if (str_copy == NULL) {
        return -1;
    }

    // 解析格式：host[:port][@token]
    char *host = str_copy;
    char *port_str = NULL;
    char *token = NULL;

    // 查找令牌分隔符
    char *at = strchr(str_copy, '@');
    if (at != NULL) {
        *at = '\0';
        token = at + 1;
    }

    // 查找端口分隔符
    char *colon = strchr(host, ':');
    if (colon != NULL) {
        *colon = '\0';
        port_str = colon + 1;
    }

    // 设置主机名
    remote_config->host = strdup(host);

    // 设置端口
    if (port_str && strlen(port_str) > 0) {
        uint32_t port;
        if (parse_size(port_str, &port) == 0 && port <= 65535) {
            remote_config->port = (uint16_t)port;
        } else {
            LOG_ERROR("Invalid port number: %s", port_str);
            free(str_copy);
            return -1;
        }
    } else {
        remote_config->port = 8888; // 默认端口
    }

    // 设置认证令牌
    if (token && strlen(token) > 0) {
        remote_config->auth_token = strdup(token);
    }

    free(str_copy);
    return 0;
}

int check_file_readable(const char *file_path) {
    if (file_path == NULL) return 0;

    FILE *file = fopen(file_path, "rb");
    if (file == NULL) {
        return 0;
    }

    fclose(file);
    return 1;
}

int check_file_writable(const char *file_path) {
    if (file_path == NULL) return 0;

    FILE *file = fopen(file_path, "ab");
    if (file == NULL) {
        return 0;
    }

    fclose(file);
    return 1;
}

long get_file_size(const char *file_path) {
    if (file_path == NULL) return -1;

    FILE *file = fopen(file_path, "rb");
    if (file == NULL) {
        return -1;
    }

    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fclose(file);

    return size;
}

void show_version(void) {
    printf("JTAG Writer Frontend v%s\n", JTAG_WRITER_VERSION);
    printf("Build Date: %s\n", JTAG_WRITER_BUILD_DATE);
    printf("Copyright (c) 2024 JTAG Writer Project\n");
}

void show_usage(void) {
    printf("Usage: jtag_writer [OPTIONS] COMMAND [COMMAND_OPTIONS]\n\n");
    printf("Commands:\n");
    printf("  program <file>     Program firmware file to target device\n");
    printf("  erase              Erase target device memory\n");
    printf("  verify <file>      Verify firmware file against target device\n");
    printf("  read <file>        Read target device memory to file\n");
    printf("  reset              Reset target device\n");
    printf("  status             Show programming status\n");
    printf("  list               List available JTAG interfaces\n");
    printf("  version            Show version information\n");
    printf("  help               Show this help message\n\n");
    printf("Global Options:\n");
    printf("  -i, --interface <interface>  JTAG interface (format: name[:config][@speed])\n");
    printf("  -t, --target <target>        Target device (format: name[:config][@addr:size])\n");
    printf("  -a, --address <addr>         Target address (hex or decimal)\n");
    printf("  -r, --remote <host>          Remote host (format: host[:port][@token])\n");
    printf("  -c, --config <file>          Configuration file\n");
    printf("  -v, --verbose                Verbose output\n");
    printf("  -n, --dry-run                Dry run (don't actually execute)\n");
    printf("  -h, --help                   Show help\n\n");
    printf("Examples:\n");
    printf("  jtag_writer -i ft2232@1000 -t stm32f4 program firmware.bin\n");
    printf("  jtag_writer -r *************:8888 program firmware.hex\n");
    printf("  jtag_writer -i jlink -t stm32f4@0x08000000:256K erase\n");
}

void show_command_help(command_type_t command_type) {
    switch (command_type) {
        case CMD_TYPE_PROGRAM:
            printf("Program Command Help:\n");
            printf("  program <firmware_file> [options]\n");
            printf("  Programs the specified firmware file to the target device.\n");
            printf("  Options:\n");
            printf("    --no-erase     Skip erase step\n");
            printf("    --no-verify    Skip verification step\n");
            printf("    --no-reset     Skip reset step\n");
            break;

        case CMD_TYPE_ERASE:
            printf("Erase Command Help:\n");
            printf("  erase [options]\n");
            printf("  Erases the target device memory.\n");
            break;

        case CMD_TYPE_VERIFY:
            printf("Verify Command Help:\n");
            printf("  verify <firmware_file> [options]\n");
            printf("  Verifies the firmware file against target device memory.\n");
            break;

        case CMD_TYPE_READ:
            printf("Read Command Help:\n");
            printf("  read <output_file> [options]\n");
            printf("  Reads target device memory to the specified file.\n");
            printf("  Options:\n");
            printf("    --length <size>    Number of bytes to read\n");
            break;

        default:
            show_usage();
            break;
    }
}
